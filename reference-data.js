// Extended content data for Web Reference Guide
const extendedContentData = {
    // Forms Section
    'form-controls': {
        title: "Form Controls",
        subtitle: "Input fields, textareas, and form elements",
        breadcrumb: ["Forms", "Form Controls"],
        content: `
            <div class="section">
                <h3>Basic Form Controls</h3>
                <p>Create accessible and styled form inputs for user interaction.</p>
                
                <div class="example-box">
                    <div class="example-header">Form Elements</div>
                    <div class="example-content">
                        <form style="max-width: 400px;">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">Text Input</label>
                                <input type="text" placeholder="Enter text" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">Email Input</label>
                                <input type="email" placeholder="Enter email" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">Textarea</label>
                                <textarea placeholder="Enter message" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; resize: vertical;"></textarea>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">Select Dropdown</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                                    <option>Choose option</option>
                                    <option>Option 1</option>
                                    <option>Option 2</option>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="insertToParent(getFormControlsCode())">📋 Insert Form Code</button>
                </div>
            </div>
        `
    },

    // Utilities Section
    'colors': {
        title: "Colors",
        subtitle: "Color utilities and palettes",
        breadcrumb: ["Utilities", "Colors"],
        content: `
            <div class="section">
                <h3>Color System</h3>
                <p>Consistent color palette for backgrounds, text, and borders.</p>
                
                <div class="example-box">
                    <div class="example-header">Color Palette</div>
                    <div class="example-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                            <div style="background: #007bff; color: white; padding: 15px; text-align: center; border-radius: 4px;">Primary</div>
                            <div style="background: #6c757d; color: white; padding: 15px; text-align: center; border-radius: 4px;">Secondary</div>
                            <div style="background: #28a745; color: white; padding: 15px; text-align: center; border-radius: 4px;">Success</div>
                            <div style="background: #dc3545; color: white; padding: 15px; text-align: center; border-radius: 4px;">Danger</div>
                            <div style="background: #ffc107; color: black; padding: 15px; text-align: center; border-radius: 4px;">Warning</div>
                            <div style="background: #17a2b8; color: white; padding: 15px; text-align: center; border-radius: 4px;">Info</div>
                        </div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="insertToParent(getColorUtilitiesCode())">📋 Insert Color Utilities</button>
                </div>
            </div>
        `
    },

    // JavaScript Section
    'js-events': {
        title: "JavaScript Events",
        subtitle: "Event handling and user interactions",
        breadcrumb: ["JavaScript", "Events"],
        content: `
            <div class="section">
                <h3>Event Handling</h3>
                <p>Handle user interactions with JavaScript event listeners.</p>
                
                <div class="example-box">
                    <div class="example-header">Common Events</div>
                    <div class="example-content">
                        <button onclick="alert('Button clicked!')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin: 5px;">Click Event</button>
                        <input type="text" placeholder="Type here..." onkeyup="console.log('Key pressed:', event.key)" style="padding: 8px; border: 1px solid #ccc; border-radius: 4px; margin: 5px;">
                        <div onmouseover="this.style.background='#e3f2fd'" onmouseout="this.style.background='#f8f9fa'" style="padding: 15px; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; margin: 5px; cursor: pointer;">Hover over me</div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="insertToParent(getEventHandlingCode())">📋 Insert Event Code</button>
                </div>
            </div>
        `
    },

    // Layout Section
    'flexbox': {
        title: "Flexbox",
        subtitle: "Flexible box layout system",
        breadcrumb: ["Layout", "Flexbox"],
        content: `
            <div class="section">
                <h3>Flexbox Layout</h3>
                <p>Create flexible and responsive layouts with CSS Flexbox.</p>
                
                <div class="example-box">
                    <div class="example-header">Flex Container</div>
                    <div class="example-content">
                        <div style="display: flex; gap: 15px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
                            <div style="flex: 1; background: #e3f2fd; padding: 20px; text-align: center; border-radius: 4px;">Flex Item 1</div>
                            <div style="flex: 2; background: #e8f5e8; padding: 20px; text-align: center; border-radius: 4px;">Flex Item 2 (2x)</div>
                            <div style="flex: 1; background: #fff3e0; padding: 20px; text-align: center; border-radius: 4px;">Flex Item 3</div>
                        </div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="insertToParent(getFlexboxCode())">📋 Insert Flexbox Code</button>
                </div>
            </div>
        `
    },

    // Components Section
    'alerts': {
        title: "Alerts",
        subtitle: "Contextual feedback messages",
        breadcrumb: ["Components", "Alerts"],
        content: `
            <div class="section">
                <h3>Alert Messages</h3>
                <p>Provide contextual feedback messages for typical user actions.</p>
                
                <div class="example-box">
                    <div class="example-header">Alert Types</div>
                    <div class="example-content">
                        <div style="background: #d4edda; color: #155724; padding: 12px; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 10px;">
                            <strong>Success!</strong> Your action was completed successfully.
                        </div>
                        <div style="background: #d1ecf1; color: #0c5460; padding: 12px; border: 1px solid #bee5eb; border-radius: 4px; margin-bottom: 10px;">
                            <strong>Info!</strong> This is some important information.
                        </div>
                        <div style="background: #fff3cd; color: #856404; padding: 12px; border: 1px solid #ffeaa7; border-radius: 4px; margin-bottom: 10px;">
                            <strong>Warning!</strong> Please check your input.
                        </div>
                        <div style="background: #f8d7da; color: #721c24; padding: 12px; border: 1px solid #f5c6cb; border-radius: 4px;">
                            <strong>Error!</strong> Something went wrong.
                        </div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="insertToParent(getAlertsCode())">📋 Insert Alert Code</button>
                </div>
            </div>
        `
    }
};

// Code generation functions for new content
function getFormControlsCode() {
    return `<form class="form-container">
    <div class="form-group">
        <label for="textInput">Text Input</label>
        <input type="text" id="textInput" class="form-control" placeholder="Enter text">
    </div>
    
    <div class="form-group">
        <label for="emailInput">Email Input</label>
        <input type="email" id="emailInput" class="form-control" placeholder="Enter email">
    </div>
    
    <div class="form-group">
        <label for="textareaInput">Textarea</label>
        <textarea id="textareaInput" class="form-control" rows="3" placeholder="Enter message"></textarea>
    </div>
    
    <div class="form-group">
        <label for="selectInput">Select Dropdown</label>
        <select id="selectInput" class="form-control">
            <option value="">Choose option</option>
            <option value="1">Option 1</option>
            <option value="2">Option 2</option>
            <option value="3">Option 3</option>
        </select>
    </div>
    
    <button type="submit" class="btn btn-primary">Submit</button>
</form>

<style>
.form-container {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}
</style>`;
}

function getColorUtilitiesCode() {
    return `<!-- Color Utility Classes -->
<div class="color-demo">
    <div class="bg-primary text-white">Primary Background</div>
    <div class="bg-secondary text-white">Secondary Background</div>
    <div class="bg-success text-white">Success Background</div>
    <div class="bg-danger text-white">Danger Background</div>
    <div class="bg-warning text-dark">Warning Background</div>
    <div class="bg-info text-white">Info Background</div>
</div>

<div class="text-colors">
    <p class="text-primary">Primary text color</p>
    <p class="text-secondary">Secondary text color</p>
    <p class="text-success">Success text color</p>
    <p class="text-danger">Danger text color</p>
    <p class="text-warning">Warning text color</p>
    <p class="text-info">Info text color</p>
</div>

<style>
/* Color Variables */
:root {
    --primary: #007bff;
    --secondary: #6c757d;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
    --light: #f8f9fa;
    --dark: #343a40;
}

/* Background Colors */
.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-info { background-color: var(--info) !important; }
.bg-light { background-color: var(--light) !important; }
.bg-dark { background-color: var(--dark) !important; }

/* Text Colors */
.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary) !important; }
.text-success { color: var(--success) !important; }
.text-danger { color: var(--danger) !important; }
.text-warning { color: var(--warning) !important; }
.text-info { color: var(--info) !important; }
.text-light { color: var(--light) !important; }
.text-dark { color: var(--dark) !important; }
.text-white { color: #fff !important; }

/* Demo Styles */
.color-demo div {
    padding: 15px;
    margin: 5px 0;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
}

.text-colors p {
    font-size: 16px;
    font-weight: 500;
    margin: 8px 0;
}
</style>`;
}

function getEventHandlingCode() {
    return `<!-- Event Handling Examples -->
<div class="event-demo">
    <button id="clickBtn" class="btn btn-primary">Click Me</button>
    <input type="text" id="keyInput" placeholder="Type here..." class="form-control">
    <div id="hoverDiv" class="hover-box">Hover over me</div>
    <div id="output" class="output-box">Event output will appear here...</div>
</div>

<style>
.event-demo {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 5px;
    transition: background 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin: 10px 0;
    box-sizing: border-box;
}

.hover-box {
    padding: 15px;
    background: #e9ecef;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    margin: 10px 0;
    transition: background 0.3s;
}

.output-box {
    padding: 10px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-top: 10px;
    min-height: 40px;
    font-family: monospace;
    font-size: 12px;
}
</style>

<script>
// Click Event
document.getElementById('clickBtn').addEventListener('click', function() {
    document.getElementById('output').textContent = 'Button was clicked at ' + new Date().toLocaleTimeString();
});

// Keyup Event
document.getElementById('keyInput').addEventListener('keyup', function(event) {
    document.getElementById('output').textContent = 'Key pressed: ' + event.key + ' | Input value: ' + this.value;
});

// Mouse Events
const hoverDiv = document.getElementById('hoverDiv');
hoverDiv.addEventListener('mouseenter', function() {
    this.style.background = '#007bff';
    this.style.color = 'white';
    document.getElementById('output').textContent = 'Mouse entered the hover box';
});

hoverDiv.addEventListener('mouseleave', function() {
    this.style.background = '#e9ecef';
    this.style.color = 'black';
    document.getElementById('output').textContent = 'Mouse left the hover box';
});

// Form Submit Event (if inside a form)
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded and events are ready!');
});
</script>`;
}

function getFlexboxCode() {
    return `<div class="flex-container">
    <div class="flex-item">Item 1</div>
    <div class="flex-item flex-grow-2">Item 2 (grows 2x)</div>
    <div class="flex-item">Item 3</div>
</div>

<div class="flex-container flex-column">
    <div class="flex-item">Vertical Item 1</div>
    <div class="flex-item">Vertical Item 2</div>
    <div class="flex-item">Vertical Item 3</div>
</div>

<div class="flex-container flex-center">
    <div class="flex-item">Centered Content</div>
</div>

<style>
.flex-container {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 15px 0;
}

.flex-item {
    background: #007bff;
    color: white;
    padding: 20px;
    border-radius: 4px;
    text-align: center;
    flex: 1;
}

/* Flex Direction */
.flex-column {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

/* Flex Grow */
.flex-grow-2 {
    flex-grow: 2;
}

.flex-grow-3 {
    flex-grow: 3;
}

/* Alignment */
.flex-center {
    justify-content: center;
    align-items: center;
    min-height: 100px;
}

.flex-space-between {
    justify-content: space-between;
}

.flex-space-around {
    justify-content: space-around;
}

.flex-start {
    justify-content: flex-start;
}

.flex-end {
    justify-content: flex-end;
}

/* Responsive */
@media (max-width: 768px) {
    .flex-container {
        flex-direction: column;
    }
    
    .flex-item {
        flex: none;
    }
}
</style>`;
}

function getAlertsCode() {
    return `<!-- Alert Messages -->
<div class="alert alert-success">
    <strong>Success!</strong> Your action was completed successfully.
    <button class="alert-close" onclick="this.parentElement.style.display='none'">&times;</button>
</div>

<div class="alert alert-info">
    <strong>Info!</strong> This is some important information.
    <button class="alert-close" onclick="this.parentElement.style.display='none'">&times;</button>
</div>

<div class="alert alert-warning">
    <strong>Warning!</strong> Please check your input.
    <button class="alert-close" onclick="this.parentElement.style.display='none'">&times;</button>
</div>

<div class="alert alert-danger">
    <strong>Error!</strong> Something went wrong.
    <button class="alert-close" onclick="this.parentElement.style.display='none'">&times;</button>
</div>

<style>
.alert {
    padding: 12px 16px;
    margin: 10px 0;
    border: 1px solid transparent;
    border-radius: 4px;
    position: relative;
    font-size: 14px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-close {
    position: absolute;
    top: 8px;
    right: 12px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.alert-close:hover {
    opacity: 1;
}

/* Alert with icons */
.alert-with-icon {
    padding-left: 40px;
}

.alert-with-icon::before {
    content: "ℹ️";
    position: absolute;
    left: 12px;
    top: 12px;
}

.alert-success.alert-with-icon::before { content: "✅"; }
.alert-warning.alert-with-icon::before { content: "⚠️"; }
.alert-danger.alert-with-icon::before { content: "❌"; }
</style>

<script>
// JavaScript function to show alerts dynamically
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = \`alert alert-\${type}\`;
    alertDiv.innerHTML = \`
        \${message}
        <button class="alert-close" onclick="this.parentElement.remove()">&times;</button>
    \`;
    
    // Insert at the top of the body or a specific container
    document.body.insertBefore(alertDiv, document.body.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

// Usage examples:
// showAlert('success', 'Operation completed successfully!');
// showAlert('danger', 'An error occurred!');
// showAlert('warning', 'Please check your input!');
// showAlert('info', 'Here is some information.');
</script>`;
}`;
}
