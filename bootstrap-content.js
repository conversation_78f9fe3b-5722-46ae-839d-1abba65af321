// Complete Bootstrap 5.3 Documentation Content
const bootstrapContent = {
    introduction: {
        title: "Get started with <PERSON>tra<PERSON>",
        subtitle: "Bootstrap is a powerful, feature-packed frontend toolkit. Build anything—from prototype to production—in minutes.",
        breadcrumb: ["Getting Started", "Introduction"],
        content: `
            <div class="cdn-info">
                <h5><i class="fas fa-rocket"></i> Quick Start</h5>
                <p>Get started by including Bootstrap's production-ready CSS and JavaScript via CDN without the need for any build steps.</p>
                
                <div class="cdn-link">
                    <strong>CSS:</strong> https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css
                </div>
                <div class="cdn-link">
                    <strong>JS:</strong> https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js
                </div>
            </div>
            
            <div class="example-container">
                <div class="example-header">
                    <span>Bootstrap Starter Template</span>
                    <button class="btn-copy" onclick="copyCode('starter-template')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="starter-template">&lt;!doctype html&gt;
&lt;html lang="en"&gt;
  &lt;head&gt;
    &lt;meta charset="utf-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1"&gt;
    &lt;title&gt;Bootstrap demo&lt;/title&gt;
    &lt;link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"&gt;
  &lt;/head&gt;
  &lt;body&gt;
    &lt;div class="container"&gt;
      &lt;h1 class="display-4"&gt;Hello, Bootstrap!&lt;/h1&gt;
      &lt;p class="lead"&gt;This is a simple Bootstrap starter template.&lt;/p&gt;
      &lt;button class="btn btn-primary"&gt;Get Started&lt;/button&gt;
    &lt;/div&gt;
    &lt;script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;
  &lt;/body&gt;
&lt;/html&gt;</pre>
                    </div>
                </div>
            </div>
            
            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapStarter())">
                    <i class="fas fa-plus"></i> Insert Starter Template
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('grid')">
                    <i class="fas fa-th"></i> Learn Grid System
                </button>
                <button class="btn btn-outline-secondary" onclick="showContent('buttons')">
                    <i class="fas fa-mouse-pointer"></i> View Components
                </button>
            </div>
        `
    },

    buttons: {
        title: "Buttons",
        subtitle: "Use Bootstrap's custom button styles for actions in forms, dialogs, and more with support for multiple sizes, states, and more.",
        breadcrumb: ["Components", "Buttons"],
        content: `
            <div class="example-container">
                <div class="example-header">
                    <span>Button Examples</span>
                    <button class="btn-copy" onclick="copyCode('button-examples')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <button type="button" class="btn btn-primary">Primary</button>
                    <button type="button" class="btn btn-secondary">Secondary</button>
                    <button type="button" class="btn btn-success">Success</button>
                    <button type="button" class="btn btn-danger">Danger</button>
                    <button type="button" class="btn btn-warning">Warning</button>
                    <button type="button" class="btn btn-info">Info</button>
                    <button type="button" class="btn btn-light">Light</button>
                    <button type="button" class="btn btn-dark">Dark</button>
                    <button type="button" class="btn btn-link">Link</button>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="button-examples">&lt;button type="button" class="btn btn-primary"&gt;Primary&lt;/button&gt;
&lt;button type="button" class="btn btn-secondary"&gt;Secondary&lt;/button&gt;
&lt;button type="button" class="btn btn-success"&gt;Success&lt;/button&gt;
&lt;button type="button" class="btn btn-danger"&gt;Danger&lt;/button&gt;
&lt;button type="button" class="btn btn-warning"&gt;Warning&lt;/button&gt;
&lt;button type="button" class="btn btn-info"&gt;Info&lt;/button&gt;
&lt;button type="button" class="btn btn-light"&gt;Light&lt;/button&gt;
&lt;button type="button" class="btn btn-dark"&gt;Dark&lt;/button&gt;
&lt;button type="button" class="btn btn-link"&gt;Link&lt;/button&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="example-container">
                <div class="example-header">
                    <span>Outline Buttons</span>
                    <button class="btn-copy" onclick="copyCode('outline-buttons')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <button type="button" class="btn btn-outline-primary">Primary</button>
                    <button type="button" class="btn btn-outline-secondary">Secondary</button>
                    <button type="button" class="btn btn-outline-success">Success</button>
                    <button type="button" class="btn btn-outline-danger">Danger</button>
                    <button type="button" class="btn btn-outline-warning">Warning</button>
                    <button type="button" class="btn btn-outline-info">Info</button>
                    <button type="button" class="btn btn-outline-dark">Dark</button>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="outline-buttons">&lt;button type="button" class="btn btn-outline-primary"&gt;Primary&lt;/button&gt;
&lt;button type="button" class="btn btn-outline-secondary"&gt;Secondary&lt;/button&gt;
&lt;button type="button" class="btn btn-outline-success"&gt;Success&lt;/button&gt;
&lt;button type="button" class="btn btn-outline-danger"&gt;Danger&lt;/button&gt;
&lt;button type="button" class="btn btn-outline-warning"&gt;Warning&lt;/button&gt;
&lt;button type="button" class="btn btn-outline-info"&gt;Info&lt;/button&gt;
&lt;button type="button" class="btn btn-outline-dark"&gt;Dark&lt;/button&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="example-container">
                <div class="example-header">
                    <span>Button Sizes</span>
                    <button class="btn-copy" onclick="copyCode('button-sizes')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <button type="button" class="btn btn-primary btn-lg">Large button</button>
                    <button type="button" class="btn btn-secondary btn-lg">Large button</button>
                    <br><br>
                    <button type="button" class="btn btn-primary">Default button</button>
                    <button type="button" class="btn btn-secondary">Default button</button>
                    <br><br>
                    <button type="button" class="btn btn-primary btn-sm">Small button</button>
                    <button type="button" class="btn btn-secondary btn-sm">Small button</button>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="button-sizes">&lt;button type="button" class="btn btn-primary btn-lg"&gt;Large button&lt;/button&gt;
&lt;button type="button" class="btn btn-secondary btn-lg"&gt;Large button&lt;/button&gt;

&lt;button type="button" class="btn btn-primary"&gt;Default button&lt;/button&gt;
&lt;button type="button" class="btn btn-secondary"&gt;Default button&lt;/button&gt;

&lt;button type="button" class="btn btn-primary btn-sm"&gt;Small button&lt;/button&gt;
&lt;button type="button" class="btn btn-secondary btn-sm"&gt;Small button&lt;/button&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapButtons())">
                    <i class="fas fa-plus"></i> Insert Button Code
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('button-group')">
                    <i class="fas fa-layer-group"></i> Button Groups
                </button>
            </div>
        `
    },

    grid: {
        title: "Grid system",
        subtitle: "Use our powerful mobile-first flexbox grid to build layouts of all shapes and sizes thanks to a twelve column system, six default responsive tiers, Sass variables and mixins, and dozens of predefined classes.",
        breadcrumb: ["Layout", "Grid"],
        content: `
            <div class="example-container">
                <div class="example-header">
                    <span>Basic Grid Example</span>
                    <button class="btn-copy" onclick="copyCode('basic-grid')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <div class="container">
                        <div class="row">
                            <div class="col" style="background: #e3f2fd; padding: 1rem; border: 1px solid #90caf9;">
                                1 of 3
                            </div>
                            <div class="col" style="background: #e8f5e8; padding: 1rem; border: 1px solid #a5d6a7;">
                                2 of 3
                            </div>
                            <div class="col" style="background: #fff3e0; padding: 1rem; border: 1px solid #ffcc02;">
                                3 of 3
                            </div>
                        </div>
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="basic-grid">&lt;div class="container"&gt;
  &lt;div class="row"&gt;
    &lt;div class="col"&gt;
      1 of 3
    &lt;/div&gt;
    &lt;div class="col"&gt;
      2 of 3
    &lt;/div&gt;
    &lt;div class="col"&gt;
      3 of 3
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="example-container">
                <div class="example-header">
                    <span>Responsive Grid</span>
                    <button class="btn-copy" onclick="copyCode('responsive-grid')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 col-md-8" style="background: #e3f2fd; padding: 1rem; border: 1px solid #90caf9;">
                                col-12 col-md-8
                            </div>
                            <div class="col-6 col-md-4" style="background: #e8f5e8; padding: 1rem; border: 1px solid #a5d6a7;">
                                col-6 col-md-4
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6 col-md-4" style="background: #fff3e0; padding: 1rem; border: 1px solid #ffcc02;">
                                col-6 col-md-4
                            </div>
                            <div class="col-6 col-md-4" style="background: #fce4ec; padding: 1rem; border: 1px solid #f8bbd9;">
                                col-6 col-md-4
                            </div>
                            <div class="col-6 col-md-4" style="background: #f3e5f5; padding: 1rem; border: 1px solid #ce93d8;">
                                col-6 col-md-4
                            </div>
                        </div>
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="responsive-grid">&lt;div class="container"&gt;
  &lt;div class="row"&gt;
    &lt;div class="col-12 col-md-8"&gt;col-12 col-md-8&lt;/div&gt;
    &lt;div class="col-6 col-md-4"&gt;col-6 col-md-4&lt;/div&gt;
  &lt;/div&gt;
  &lt;div class="row"&gt;
    &lt;div class="col-6 col-md-4"&gt;col-6 col-md-4&lt;/div&gt;
    &lt;div class="col-6 col-md-4"&gt;col-6 col-md-4&lt;/div&gt;
    &lt;div class="col-6 col-md-4"&gt;col-6 col-md-4&lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="cdn-info">
                <h5><i class="fas fa-info-circle"></i> Grid Breakpoints</h5>
                <p>Bootstrap includes six default breakpoints for building responsively:</p>
                <ul>
                    <li><strong>xs:</strong> &lt;576px</li>
                    <li><strong>sm:</strong> ≥576px</li>
                    <li><strong>md:</strong> ≥768px</li>
                    <li><strong>lg:</strong> ≥992px</li>
                    <li><strong>xl:</strong> ≥1200px</li>
                    <li><strong>xxl:</strong> ≥1400px</li>
                </ul>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapGrid())">
                    <i class="fas fa-plus"></i> Insert Grid Code
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('containers')">
                    <i class="fas fa-square"></i> Learn Containers
                </button>
            </div>
        `
    },

    card: {
        title: "Cards",
        subtitle: "Bootstrap's cards provide a flexible and extensible content container with multiple variants and options.",
        breadcrumb: ["Components", "Cards"],
        content: `
            <div class="example-container">
                <div class="example-header">
                    <span>Basic Card</span>
                    <button class="btn-copy" onclick="copyCode('basic-card')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <div class="card" style="width: 18rem;">
                        <img src="https://via.placeholder.com/286x180" class="card-img-top" alt="...">
                        <div class="card-body">
                            <h5 class="card-title">Card title</h5>
                            <p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
                            <a href="#" class="btn btn-primary">Go somewhere</a>
                        </div>
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="basic-card">&lt;div class="card" style="width: 18rem;"&gt;
  &lt;img src="..." class="card-img-top" alt="..."&gt;
  &lt;div class="card-body"&gt;
    &lt;h5 class="card-title"&gt;Card title&lt;/h5&gt;
    &lt;p class="card-text"&gt;Some quick example text to build on the card title and make up the bulk of the card's content.&lt;/p&gt;
    &lt;a href="#" class="btn btn-primary"&gt;Go somewhere&lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="example-container">
                <div class="example-header">
                    <span>Card with Header and Footer</span>
                    <button class="btn-copy" onclick="copyCode('card-header-footer')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <div class="card">
                        <div class="card-header">
                            Featured
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">Special title treatment</h5>
                            <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>
                            <a href="#" class="btn btn-primary">Go somewhere</a>
                        </div>
                        <div class="card-footer text-body-secondary">
                            2 days ago
                        </div>
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="card-header-footer">&lt;div class="card"&gt;
  &lt;div class="card-header"&gt;
    Featured
  &lt;/div&gt;
  &lt;div class="card-body"&gt;
    &lt;h5 class="card-title"&gt;Special title treatment&lt;/h5&gt;
    &lt;p class="card-text"&gt;With supporting text below as a natural lead-in to additional content.&lt;/p&gt;
    &lt;a href="#" class="btn btn-primary"&gt;Go somewhere&lt;/a&gt;
  &lt;/div&gt;
  &lt;div class="card-footer text-body-secondary"&gt;
    2 days ago
  &lt;/div&gt;
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapCard())">
                    <i class="fas fa-plus"></i> Insert Card Code
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('carousel')">
                    <i class="fas fa-images"></i> View Carousel
                </button>
            </div>
        `
    },

    navbar: {
        title: "Navbar",
        subtitle: "Documentation and examples for Bootstrap's powerful, responsive navigation header, the navbar. Includes support for branding, navigation, and more, including support for our collapse plugin.",
        breadcrumb: ["Components", "Navbar"],
        content: `
            <div class="example-container">
                <div class="example-header">
                    <span>Basic Navbar</span>
                    <button class="btn-copy" onclick="copyCode('basic-navbar')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                        <div class="container-fluid">
                            <a class="navbar-brand" href="#">Navbar</a>
                            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                                <span class="navbar-toggler-icon"></span>
                            </button>
                            <div class="collapse navbar-collapse" id="navbarNav">
                                <ul class="navbar-nav">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#">Home</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">Features</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">Pricing</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </nav>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="basic-navbar">&lt;nav class="navbar navbar-expand-lg navbar-dark bg-dark"&gt;
  &lt;div class="container-fluid"&gt;
    &lt;a class="navbar-brand" href="#"&gt;Navbar&lt;/a&gt;
    &lt;button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"&gt;
      &lt;span class="navbar-toggler-icon"&gt;&lt;/span&gt;
    &lt;/button&gt;
    &lt;div class="collapse navbar-collapse" id="navbarNav"&gt;
      &lt;ul class="navbar-nav"&gt;
        &lt;li class="nav-item"&gt;
          &lt;a class="nav-link active" href="#"&gt;Home&lt;/a&gt;
        &lt;/li&gt;
        &lt;li class="nav-item"&gt;
          &lt;a class="nav-link" href="#"&gt;Features&lt;/a&gt;
        &lt;/li&gt;
        &lt;li class="nav-item"&gt;
          &lt;a class="nav-link" href="#"&gt;Pricing&lt;/a&gt;
        &lt;/li&gt;
      &lt;/ul&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/nav&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapNavbar())">
                    <i class="fas fa-plus"></i> Insert Navbar Code
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('dropdowns')">
                    <i class="fas fa-caret-down"></i> View Dropdowns
                </button>
            </div>
        `
    },

    modal: {
        title: "Modal",
        subtitle: "Use Bootstrap's JavaScript modal plugin to add dialogs to your site for lightboxes, user notifications, or completely custom content.",
        breadcrumb: ["Components", "Modal"],
        content: `
            <div class="example-container">
                <div class="example-header">
                    <span>Modal Example</span>
                    <button class="btn-copy" onclick="copyCode('modal-example')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">
                        Launch demo modal
                    </button>

                    <div class="modal fade" id="exampleModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h1 class="modal-title fs-5">Modal title</h1>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    Modal body text goes here.
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary">Save changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="modal-example">&lt;!-- Button trigger modal --&gt;
&lt;button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal"&gt;
  Launch demo modal
&lt;/button&gt;

&lt;!-- Modal --&gt;
&lt;div class="modal fade" id="exampleModal" tabindex="-1"&gt;
  &lt;div class="modal-dialog"&gt;
    &lt;div class="modal-content"&gt;
      &lt;div class="modal-header"&gt;
        &lt;h1 class="modal-title fs-5"&gt;Modal title&lt;/h1&gt;
        &lt;button type="button" class="btn-close" data-bs-dismiss="modal"&gt;&lt;/button&gt;
      &lt;/div&gt;
      &lt;div class="modal-body"&gt;
        Modal body text goes here.
      &lt;/div&gt;
      &lt;div class="modal-footer"&gt;
        &lt;button type="button" class="btn btn-secondary" data-bs-dismiss="modal"&gt;Close&lt;/button&gt;
        &lt;button type="button" class="btn btn-primary"&gt;Save changes&lt;/button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapModal())">
                    <i class="fas fa-plus"></i> Insert Modal Code
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('offcanvas')">
                    <i class="fas fa-bars"></i> View Offcanvas
                </button>
            </div>
        `
    },

    alerts: {
        title: "Alerts",
        subtitle: "Provide contextual feedback messages for typical user actions with the handful of available and flexible alert messages.",
        breadcrumb: ["Components", "Alerts"],
        content: `
            <div class="example-container">
                <div class="example-header">
                    <span>Alert Examples</span>
                    <button class="btn-copy" onclick="copyCode('alert-examples')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <div class="alert alert-primary" role="alert">
                        A simple primary alert—check it out!
                    </div>
                    <div class="alert alert-secondary" role="alert">
                        A simple secondary alert—check it out!
                    </div>
                    <div class="alert alert-success" role="alert">
                        A simple success alert—check it out!
                    </div>
                    <div class="alert alert-danger" role="alert">
                        A simple danger alert—check it out!
                    </div>
                    <div class="alert alert-warning" role="alert">
                        A simple warning alert—check it out!
                    </div>
                    <div class="alert alert-info" role="alert">
                        A simple info alert—check it out!
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="alert-examples">&lt;div class="alert alert-primary" role="alert"&gt;
  A simple primary alert—check it out!
&lt;/div&gt;
&lt;div class="alert alert-secondary" role="alert"&gt;
  A simple secondary alert—check it out!
&lt;/div&gt;
&lt;div class="alert alert-success" role="alert"&gt;
  A simple success alert—check it out!
&lt;/div&gt;
&lt;div class="alert alert-danger" role="alert"&gt;
  A simple danger alert—check it out!
&lt;/div&gt;
&lt;div class="alert alert-warning" role="alert"&gt;
  A simple warning alert—check it out!
&lt;/div&gt;
&lt;div class="alert alert-info" role="alert"&gt;
  A simple info alert—check it out!
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="example-container">
                <div class="example-header">
                    <span>Dismissible Alerts</span>
                    <button class="btn-copy" onclick="copyCode('dismissible-alerts')"><i class="fas fa-copy"></i> Copy</button>
                </div>
                <div class="example-content">
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <strong>Holy guacamole!</strong> You should check in on some of those fields below.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
                <div class="code-container">
                    <div class="code-content">
                        <pre id="dismissible-alerts">&lt;div class="alert alert-warning alert-dismissible fade show" role="alert"&gt;
  &lt;strong&gt;Holy guacamole!&lt;/strong&gt; You should check in on some of those fields below.
  &lt;button type="button" class="btn-close" data-bs-dismiss="alert"&gt;&lt;/button&gt;
&lt;/div&gt;</pre>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" onclick="insertToParent(getBootstrapAlerts())">
                    <i class="fas fa-plus"></i> Insert Alert Code
                </button>
                <button class="btn btn-outline-primary" onclick="showContent('badge')">
                    <i class="fas fa-tag"></i> View Badges
                </button>
            </div>
        `
    }
};

// JavaScript functions for Bootstrap content
function showContent(contentId) {
    const content = bootstrapContent[contentId];
    if (!content) {
        // Show placeholder for content not yet implemented
        document.getElementById('mainContent').innerHTML = `
            <div class="content-header">
                <div class="breadcrumb-custom">Bootstrap > ${contentId}</div>
                <h1 class="content-title">${contentId.charAt(0).toUpperCase() + contentId.slice(1).replace('-', ' ')}</h1>
                <p class="content-subtitle">This section is being prepared with complete Bootstrap documentation.</p>
            </div>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Coming Soon</h5>
                <p>This section will contain the complete Bootstrap documentation for ${contentId}. In the meantime, you can:</p>
                <ul>
                    <li>Visit the <a href="https://getbootstrap.com/docs/5.3/${contentId.replace('-', '/')}" target="_blank">official Bootstrap documentation</a></li>
                    <li>Use the search function to find related content</li>
                    <li>Explore other available sections</li>
                </ul>
            </div>
        `;
        return;
    }
    
    // Update active nav item
    document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
    event.target.classList.add('active');
    
    // Update main content
    document.getElementById('mainContent').innerHTML = `
        <div class="content-header">
            <div class="breadcrumb-custom">${content.breadcrumb.join(' > ')}</div>
            <h1 class="content-title">${content.title}</h1>
            <p class="content-subtitle">${content.subtitle}</p>
        </div>
        ${content.content}
    `;
}

function searchContent() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        const section = item.closest('.nav-section');
        
        if (text.includes(searchTerm)) {
            item.style.display = 'block';
            section.style.display = 'block';
        } else {
            item.style.display = searchTerm ? 'none' : 'block';
        }
    });
    
    // Show/hide sections based on whether they have visible items
    document.querySelectorAll('.nav-section').forEach(section => {
        const visibleItems = section.querySelectorAll('.nav-item:not([style*="display: none"])');
        section.style.display = visibleItems.length > 0 || !searchTerm ? 'block' : 'none';
    });
}

function copyCode(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        // Show success feedback
        const button = event.target.closest('.btn-copy');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.style.color = '#28a745';
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.style.color = '';
        }, 2000);
    });
}

function insertToParent(code) {
    if (window.parent && window.parent.koli) {
        const currentCode = window.parent.koli.getValue();
        const newCode = currentCode + '\n\n' + code;
        window.parent.koli.setValue(newCode);
        window.parent.koli.clearSelection();
        
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = '<i class="fas fa-check"></i> Code inserted into editor successfully!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(code).then(() => {
            alert('Code copied to clipboard!');
        });
    }
}

// Code generation functions
function getBootstrapStarter() {
    return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Bootstrap demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
  </head>
  <body>
    <div class="container">
      <h1 class="display-4">Hello, Bootstrap!</h1>
      <p class="lead">This is a simple Bootstrap starter template.</p>
      <button class="btn btn-primary">Get Started</button>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
  </body>
</html>`;
}

function getBootstrapButtons() {
    return `<!-- Bootstrap Buttons -->
<button type="button" class="btn btn-primary">Primary</button>
<button type="button" class="btn btn-secondary">Secondary</button>
<button type="button" class="btn btn-success">Success</button>
<button type="button" class="btn btn-danger">Danger</button>
<button type="button" class="btn btn-warning">Warning</button>
<button type="button" class="btn btn-info">Info</button>

<!-- Outline Buttons -->
<button type="button" class="btn btn-outline-primary">Primary</button>
<button type="button" class="btn btn-outline-secondary">Secondary</button>

<!-- Button Sizes -->
<button type="button" class="btn btn-primary btn-lg">Large button</button>
<button type="button" class="btn btn-primary">Default button</button>
<button type="button" class="btn btn-primary btn-sm">Small button</button>

<!-- Include Bootstrap CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>`;
}

function getBootstrapGrid() {
    return `<!-- Bootstrap Grid System -->
<div class="container">
  <div class="row">
    <div class="col">
      1 of 3
    </div>
    <div class="col">
      2 of 3
    </div>
    <div class="col">
      3 of 3
    </div>
  </div>
  
  <!-- Responsive Grid -->
  <div class="row">
    <div class="col-12 col-md-8">col-12 col-md-8</div>
    <div class="col-6 col-md-4">col-6 col-md-4</div>
  </div>
  
  <!-- Specific Column Widths -->
  <div class="row">
    <div class="col-6 col-md-4">col-6 col-md-4</div>
    <div class="col-6 col-md-4">col-6 col-md-4</div>
    <div class="col-6 col-md-4">col-6 col-md-4</div>
  </div>
</div>

<!-- Include Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">`;
}

function getBootstrapCard() {
    return `<!-- Bootstrap Card -->
<div class="card" style="width: 18rem;">
  <img src="https://via.placeholder.com/286x180" class="card-img-top" alt="...">
  <div class="card-body">
    <h5 class="card-title">Card title</h5>
    <p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
    <a href="#" class="btn btn-primary">Go somewhere</a>
  </div>
</div>

<!-- Card with Header and Footer -->
<div class="card">
  <div class="card-header">
    Featured
  </div>
  <div class="card-body">
    <h5 class="card-title">Special title treatment</h5>
    <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>
    <a href="#" class="btn btn-primary">Go somewhere</a>
  </div>
  <div class="card-footer text-body-secondary">
    2 days ago
  </div>
</div>

<!-- Include Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">`;
}

function getBootstrapNavbar() {
    return `<!-- Bootstrap Navbar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <div class="container-fluid">
    <a class="navbar-brand" href="#">Your Brand</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page" href="#">Home</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">Features</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">Pricing</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">About</a>
        </li>
      </ul>
    </div>
  </div>
</nav>

<!-- Include Bootstrap CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>`;
}

function getBootstrapModal() {
    return `<!-- Bootstrap Modal -->
<!-- Button trigger modal -->
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">
  Launch demo modal
</button>

<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="exampleModalLabel">Modal title</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Modal body text goes here. You can add any content like forms, images, or other components.</p>
        <p>This modal is fully responsive and accessible.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Save changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Include Bootstrap CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>`;
}

function getBootstrapAlerts() {
    return `<!-- Bootstrap Alerts -->
<div class="alert alert-primary" role="alert">
  A simple primary alert—check it out!
</div>

<div class="alert alert-success" role="alert">
  <h4 class="alert-heading">Well done!</h4>
  <p>Aww yeah, you successfully read this important alert message. This example text is going to run a bit longer so that you can see how spacing within an alert works with this kind of content.</p>
  <hr>
  <p class="mb-0">Whenever you need to, be sure to use margin utilities to keep things nice and tidy.</p>
</div>

<div class="alert alert-warning alert-dismissible fade show" role="alert">
  <strong>Holy guacamole!</strong> You should check in on some of those fields below.
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>

<div class="alert alert-danger" role="alert">
  <strong>Error!</strong> Something went wrong. Please try again.
</div>

<div class="alert alert-info" role="alert">
  <strong>Info:</strong> This is some important information you should know.
</div>

<!-- Include Bootstrap CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>`;
}
