<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - AI Features</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Compiler Feature Test</h1>
        
        <div class="test-section">
            <h3>✅ Basic Features Status</h3>
            <div id="basicStatus" class="status info">Testing basic functionality...</div>
            <button onclick="testBasicFeatures()">Test Basic Features</button>
        </div>
        
        <div class="test-section">
            <h3>🤖 AI Code Generator Test</h3>
            <div id="aiStatus" class="status info">Ready to test AI features...</div>
            <button onclick="testAIFeatures()">Test AI Generator</button>
        </div>
        
        <div class="test-section">
            <h3>📚 Component Library Test</h3>
            <div id="componentStatus" class="status info">Ready to test component library...</div>
            <button onclick="testComponentLibrary()">Test Component Library</button>
        </div>
        
        <div class="test-section">
            <h3>🔗 Quick Links</h3>
            <button onclick="window.open('index.html', '_blank')">Open Main Compiler</button>
            <button onclick="window.open('demo.html', '_blank')">View Demo Page</button>
        </div>
        
        <div class="test-section">
            <h3>📋 Instructions</h3>
            <ol>
                <li><strong>Open Main Compiler:</strong> Click "Open Main Compiler" button above</li>
                <li><strong>Test Circular Menus:</strong> Click the circular menu buttons (left = blue, right = red)</li>
                <li><strong>Test AI Generator:</strong> Click the magic wand icon (🪄) in the right menu</li>
                <li><strong>Test Component Library:</strong> Click the code icon (</>) in the right menu</li>
                <li><strong>Test Live Preview:</strong> Press Ctrl+I to toggle preview panel</li>
            </ol>
        </div>
    </div>

    <script>
        function testBasicFeatures() {
            const status = document.getElementById('basicStatus');
            status.className = 'status info';
            status.textContent = 'Testing basic features...';
            
            setTimeout(() => {
                // Test if basic functions exist
                const tests = [
                    typeof openCustomFrame === 'function',
                    typeof closeCustomFrame === 'function',
                    typeof resizeCustomFrame === 'function'
                ];
                
                if (tests.every(test => test)) {
                    status.className = 'status success';
                    status.textContent = '✅ Basic features are working correctly!';
                } else {
                    status.className = 'status error';
                    status.textContent = '❌ Some basic features are missing. Check console for errors.';
                }
            }, 1000);
        }
        
        function testAIFeatures() {
            const status = document.getElementById('aiStatus');
            status.className = 'status info';
            status.textContent = 'Testing AI features...';

            setTimeout(() => {
                // Test if AI HTML file exists by trying to load it
                fetch('ai-generator.html')
                    .then(response => {
                        if (response.ok) {
                            status.className = 'status success';
                            status.textContent = '✅ AI Generator HTML file exists and is accessible!';
                        } else {
                            status.className = 'status error';
                            status.textContent = '❌ AI Generator HTML file not found.';
                        }
                    })
                    .catch(error => {
                        status.className = 'status error';
                        status.textContent = '❌ Error loading AI Generator: ' + error.message;
                    });
            }, 1000);
        }

        function testComponentLibrary() {
            const status = document.getElementById('componentStatus');
            status.className = 'status info';
            status.textContent = 'Testing component library...';

            setTimeout(() => {
                // Test if Component Library HTML file exists by trying to load it
                fetch('component-library.html')
                    .then(response => {
                        if (response.ok) {
                            status.className = 'status success';
                            status.textContent = '✅ Component Library HTML file exists and is accessible!';
                        } else {
                            status.className = 'status error';
                            status.textContent = '❌ Component Library HTML file not found.';
                        }
                    })
                    .catch(error => {
                        status.className = 'status error';
                        status.textContent = '❌ Error loading Component Library: ' + error.message;
                    });
            }, 1000);
        }
        
        // Auto-run basic test on page load
        window.onload = function() {
            setTimeout(testBasicFeatures, 500);
        };
    </script>
</body>
</html>
