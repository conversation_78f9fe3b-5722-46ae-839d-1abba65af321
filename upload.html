<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Merge HTML, CSS, JS Files</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    #dropzone {
      border: 2px dashed #888;
      padding: 30px;
      text-align: center;
      margin-bottom: 20px;
      color: #555;
      cursor: pointer;
      transition: background 0.2s, border-color 0.2s;
    }
    #dropzone.dragover {
      background-color: #f0f0f0;
      border-color: #333;
    }
    textarea {
      width: 100%;
      height: 400px;
      font-family: monospace;
      margin-top: 20px;
      resize: vertical;
    }
    button {
      margin-top: 10px;
      padding: 10px 15px;
      font-size: 16px;
      cursor: pointer;
    }
  </style>
</head>
<body>

  <h2>Upload HTML, CSS, JS and Merge into One</h2>

  <div id="dropzone">Click or Drag &amp; Drop <strong>index.html</strong>, <strong>style.css</strong>, <strong>script.js</strong></div>
  <input type="file" id="fileInput" multiple accept=".html,.css,.js" style="display: none;">

  <textarea id="output" placeholder="Combined HTML code will appear here..."></textarea>
  <br>
  <button onclick="copyToClipboard()">📋 Copy Combined Code</button>

  <script>
    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('fileInput');
    const output = document.getElementById('output');

    let filesMap = {};

    dropzone.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', () => handleFiles(fileInput.files));

    dropzone.addEventListener('dragover', e => {
      e.preventDefault();
      dropzone.classList.add('dragover');
    });

    dropzone.addEventListener('dragleave', () => {
      dropzone.classList.remove('dragover');
    });

    dropzone.addEventListener('drop', e => {
      e.preventDefault();
      dropzone.classList.remove('dragover');
      handleFiles(e.dataTransfer.files);
    });

    async function handleFiles(fileList) {
      filesMap = {};
      for (let file of fileList) {
        filesMap[file.name] = file;
      }

      if (!filesMap["index.html"]) {
        alert("You must include an index.html file!");
        return;
      }

      let htmlText = await readFile(filesMap["index.html"]);

      // Inline CSS files
      for (let name in filesMap) {
        if (name.endsWith(".css")) {
          const cssText = await readFile(filesMap[name]);
          // Replace <link rel="stylesheet" href="name"> with <style>...</style>
          const cssTag = new RegExp('<link[^>]*href=["\']' + name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&') + '["\'][^>]*>', 'gi');
          htmlText = htmlText.replace(cssTag, '<style>\n' + escapeContent(cssText) + '\n</style>');
        }
      }

      // Inline JS files
      for (let name in filesMap) {
        if (name.endsWith(".js")) {
          const jsText = await readFile(filesMap[name]);
          // Replace <script src="name"></script> with <script>...</script>
          const jsTag = new RegExp('<script[^>]*src=["\']' + name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&') + '["\'][^>]*>\\s*</script>', 'gi');
          htmlText = htmlText.replace(jsTag, '<script>\n' + escapeContent(jsText) + '\n</script>');
        }
      }

      output.value = htmlText;
    }

    function readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = reject;
        reader.readAsText(file);
      });
    }

    function escapeContent(text) {
      // Basic escape to avoid accidental </script> or </style>
      return text.replace(/<\/script>/gi, '<\\/script>').replace(/<\/style>/gi, '<\\/style>');
    }

    function copyToClipboard() {
      output.select();
      document.execCommand("copy");
      alert("✅ Code copied to clipboard!");
    }
  </script>

</body>
</html>
