 /*Editor css */ 
body { overflow:hidden; margin:0;  }
    .Input #editor::-webkit-scrollbar{
      width:thin;
    }
    .Input #editor::-webkit-scrollbar-thumb {
       background:red
    } 
 .Input #editor { 
   scrollbar-color: rgba(224, 224, 224, 0.67) rgba(237, 237, 237, 1);
    position:absolute;
     bottom:0;
     left:0;
   right:20%;
 font-weight: normal;
  color:rgba(18, 18, 18, 0.98);
  /*font-family : sans-serif !important; font-size:50px!important;  direction:1tr !important;text-align:left !important;*/
  scrollbar-width: thin;
    }
  .same { 
       top:100px;
       border :solid 3px;
       margin: 5px; 
       padding: 0px; 
       border-radius: 10px;
       
       /* cosmize Height top buttom */ 
  }     
  .output{ position:absolute;left:80%;right:0; bottom:0; 
        box-shadow: -3px 2px 20px 0px hsla(0, 0%, 80%, 0.82); 
       border-left:inset 10px rgba(255, 255, 255, 0); 
        touch-action: none;
     box-sizing: border-box; 
    }  
    .ace_gutter-layer  { background:white}
    
   .output iframe {border:none; box-sizing: border-box;display: block; touch-action: none;
       margin:0; width:100%; overflow-x:hidden; height:100%; 
    }
    .output .ss { position:absolute; top:0; bottom:0; left:0; right:0;display:none; 
      
    }

/*Two circular menu buttons */ 

.circular-menu {
  position: absolute;
  bottom: 1em;
  right: 1em;
  z-index:9;
  
}

.circular-menu .floating-btn {
  display: block;
  width: 3.5em;
  height: 3.5em;
  border-radius: 50%;
  background-color: hsl(4, 98%, 60%);
  box-shadow: 0 2px 5px 0 hsla(0, 0%, 0%, .26);  
  color: hsl(0, 0%, 100%);
  text-align: center;
  line-height: 3.9;
  cursor: pointer;
  outline: 0;
  
}

.circular-menu.active .floating-btn {
  box-shadow: inset 0 0 3px hsla(0, 0%, 0%, .3);
}

.circular-menu .floating-btn:active {
  box-shadow: 0 4px 8px 0 hsla(0, 0%, 0%, .4);
}

.circular-menu .floating-btn i {
  font-size: 1.3em;
  transition: transform .2s;  
}

.circular-menu.active .floating-btn i {
  transform: rotate(-45deg);
}

.circular-menu:after {
  display: block;
  content: ' ';
  width: 3.5em;
  height: 3.5em;
  border-radius: 50%;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -2;
  background-color: hsl(4, 98%, 60%);
  transition: all .3s ease;
}

.circular-menu.active:after {
  transform: scale3d(5.5, 5.5, 1);
  transition-timing-function: cubic-bezier(.68, 1.55, .265, 1);
}

.circular-menu .items-wrapper {
  padding: 0;
  margin: 0;
}

.circular-menu .menu-item {
  position: absolute;
  top: .2em;
  right: .2em;
  z-index: -1;
  display: block;
  text-decoration: none;
  color: hsl(0, 0%, 100%);
  font-size: 1em;
  width: 3em;
  height: 3em;
  border-radius: 50%;
  text-align: center;
  line-height: 3;
  background-color: hsla(0,0%,0%,.1);
  transition: transform .3s ease, background .2s ease;
}

.circular-menu .menu-item:hover {
  background-color: hsla(0,0%,0%,.3);
}

.circular-menu.active .menu-item {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.circular-menu.active .menu-item:nth-child(1) {
  transform: translate3d(1em,-7em,0);
}

.circular-menu.active .menu-item:nth-child(2) {
  transform: translate3d(-3.5em,-6.3em,0);
}

.circular-menu.active .menu-item:nth-child(3) {
  transform: translate3d(-6.5em,-3.2em,0);
}

.circular-menu.active .menu-item:nth-child(4) {
  transform: translate3d(-7em,1em,0);
}

/**
 * The other theme for this menu
 */

.circular-menu.circular-menu-left {
  right: auto; 
  left: 1em;
}

.circular-menu.circular-menu-left .floating-btn {
  background-color: hsl(217, 89%, 61%);
}

.circular-menu.circular-menu-left:after {
  background-color: hsl(217, 89%, 61%);
}

.circular-menu.circular-menu-left.active .floating-btn i {
  transform: rotate(90deg);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(1) {
  transform: translate3d(-1em,-7em,0);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(2) {
  transform: translate3d(3.5em,-6.3em,0);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(3) {
  transform: translate3d(6.5em,-3.2em,0);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(4) {
  transform: translate3d(7em,1em,0);
}
   
 /*Simple frame */
    .custom-frame {
      border: 1px solid #000;
      width: 90%;
      overflow: auto;
      border-radius: 10px;
      background: White;
      position: fixed;
      right:5% ;
      left: 5%;
      top: 50px;
    }
    .custom-size{
        width: 90%;
        height: 2px;
    }
    .-close {
        position: fixed;
       right: 7%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #ff4b2b 0%, #ff416c 100%);
        border: none;
        border-radius: 50%;
        color: #fff;
        font-size: 28px;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(255, 65, 108, 0.15);
        cursor: pointer;
        transition: background 0.3s, transform 0.2s, box-shadow 0.2s;
        z-index: 10;
    }
    .-close:hover, .-close:focus {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        color: #fff;
        transform: scale(1.1) rotate(10deg);
        box-shadow: 0 6px 18px rgba(255, 65, 108, 0.25);
        outline: none;
    }

   /*Note pad */
  button {
            background-color: #007bff;
            color: #fff;
            border: none;
            padding: 5px 10px;
            margin: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        select, input[type="color"] {
            padding: 5px;
        }

        textarea {
            width: 96%;
            height: 400px;
            resize: none;
            border-radius: 7px;
        }

        #pageIndicator {
            font-weight: bold;
            margin: 10px 0;
        }

        .page-transition {
            transition: opacity 0.3s;
        }  
    
    
    
     /* Admin cantent  */
   

    .iframe-container {
      border: 1px solid #000;
      width: 90%;
      overflow: auto;
      border-radius: 10px;
      background: White;
      position: fixed;
      display:none;
      right:5% ;
      left: 5%;
      top: 50px;
      
    }

    .popup {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid #000;
      background: #fff;
      padding: 20px;
    }
    
    
/* Admin css */
  .custom-content {
    border: 1px solid #000;
    padding: 20px;
    margin: 20px;
  }

  .custom-iframe-container {
    border: 1px solid #000;
    width: 90%;
    overflow: auto;
    border-radius: 10px;
    background: White;
    position: fixed;
    right:5% ;
    left: 5%;
    top: 50px;
    display:none;
  }

  .custom-popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid #000;
    background: #fff;
    padding: 20px;
  }
  
 /* html to file */ 
  .text {
            border-radius: 10px;width: 90%;height: 400px;
        }

/* copy  code css*/
  .copycode {
            white-space: nowrap;
            display: block;
            background-color: #f4f4f4;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            background: #828282;
            overflow-x: auto;
            border-radius: 5px;
            height: auto;
            font-family: 'Courier New', monospace;
        }

/* AI Code Generator Styles */
.ai-generator-container {
    padding: 20px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

.ai-input-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.ai-input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

#aiPrompt {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    resize: vertical;
    box-sizing: border-box;
}

.ai-options {
    margin: 15px 0;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.ai-options label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
}

#generateBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: transform 0.2s, box-shadow 0.2s;
}

#generateBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

#generateBtn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.ai-status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
}

.ai-status.loading {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.ai-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ai-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.ai-output-section {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.ai-output-header {
    background: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-output-header h3 {
    margin: 0;
    color: #333;
}

#insertBtn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

#insertBtn:hover {
    background: #218838;
}

.ai-code-output {
    background: #f8f9fa;
    padding: 15px;
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
}

/* Component Library Styles */
.component-library-container {
    padding: 20px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

.component-search-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

#componentSearch {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    margin-bottom: 15px;
    box-sizing: border-box;
}

.component-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.filter-btn:hover {
    background: #f8f9fa;
}

.filter-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.component-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.component-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
}

.component-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    transform: translateY(-2px);
}

.component-item h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
}

.component-item .component-type {
    display: inline-block;
    padding: 2px 8px;
    background: #e9ecef;
    border-radius: 12px;
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.component-item .component-type.html {
    background: #fff3cd;
    color: #856404;
}

.component-item .component-type.css {
    background: #d4edda;
    color: #155724;
}

.component-item .component-type.javascript {
    background: #d1ecf1;
    color: #0c5460;
}

.component-item .component-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.component-item .component-preview {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #333;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.component-item .component-actions {
    margin-top: 10px;
    display: flex;
    gap: 8px;
}

.component-item .insert-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.component-item .insert-btn:hover {
    background: #218838;
}

.component-item .preview-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.component-item .preview-btn:hover {
    background: #138496;
}

/* Additional polish for new features */
.ai-generator-container h2,
.component-library-container h2 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
}

/* Scrollbar styling for component results */
.component-results::-webkit-scrollbar {
    width: 8px;
}

.component-results::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.component-results::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.component-results::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading animation for AI generation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#generateBtn:disabled::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Improved menu item tooltips */
.menu-item {
    position: relative;
}

.menu-item::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.menu-item:hover::after {
    opacity: 1;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .ai-options {
        flex-direction: column;
        gap: 10px;
    }

    .component-filters {
        justify-content: center;
    }

    .component-results {
        grid-template-columns: 1fr;
    }

    .ai-output-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}






        
        