 /*Editor css */ 
body { overflow:hidden; margin:0;  }
    .Input #editor::-webkit-scrollbar{
      width:thin;
    }
    .Input #editor::-webkit-scrollbar-thumb {
       background:red
    } 
 .Input #editor { 
   scrollbar-color: rgba(224, 224, 224, 0.67) rgba(237, 237, 237, 1);
    position:absolute;
     bottom:0;
     left:0;
   right:20%;
 font-weight: normal;
  color:rgba(18, 18, 18, 0.98);
  /*font-family : sans-serif !important; font-size:50px!important;  direction:1tr !important;text-align:left !important;*/
  scrollbar-width: thin;
    }
  .same { 
       top:100px;
       border :solid 3px;
       margin: 5px; 
       padding: 0px; 
       border-radius: 10px;
       
       /* cosmize Height top buttom */ 
  }     
  .output{ position:absolute;left:80%;right:0; bottom:0; 
        box-shadow: -3px 2px 20px 0px hsla(0, 0%, 80%, 0.82); 
       border-left:inset 10px rgba(255, 255, 255, 0); 
        touch-action: none;
     box-sizing: border-box; 
    }  
    .ace_gutter-layer  { background:white}
    
   .output iframe {border:none; box-sizing: border-box;display: block; touch-action: none;
       margin:0; width:100%; overflow-x:hidden; height:100%; 
    }
    .output .ss { position:absolute; top:0; bottom:0; left:0; right:0;display:none; 
      
    }

/*Two circular menu buttons */ 

.circular-menu {
  position: absolute;
  bottom: 1em;
  right: 1em;
  z-index:9;
  
}

.circular-menu .floating-btn {
  display: block;
  width: 3.5em;
  height: 3.5em;
  border-radius: 50%;
  background-color: hsl(4, 98%, 60%);
  box-shadow: 0 2px 5px 0 hsla(0, 0%, 0%, .26);  
  color: hsl(0, 0%, 100%);
  text-align: center;
  line-height: 3.9;
  cursor: pointer;
  outline: 0;
  
}

.circular-menu.active .floating-btn {
  box-shadow: inset 0 0 3px hsla(0, 0%, 0%, .3);
}

.circular-menu .floating-btn:active {
  box-shadow: 0 4px 8px 0 hsla(0, 0%, 0%, .4);
}

.circular-menu .floating-btn i {
  font-size: 1.3em;
  transition: transform .2s;  
}

.circular-menu.active .floating-btn i {
  transform: rotate(-45deg);
}

.circular-menu:after {
  display: block;
  content: ' ';
  width: 3.5em;
  height: 3.5em;
  border-radius: 50%;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -2;
  background-color: hsl(4, 98%, 60%);
  transition: all .3s ease;
}

.circular-menu.active:after {
  transform: scale3d(5.5, 5.5, 1);
  transition-timing-function: cubic-bezier(.68, 1.55, .265, 1);
}

.circular-menu .items-wrapper {
  padding: 0;
  margin: 0;
}

.circular-menu .menu-item {
  position: absolute;
  top: .2em;
  right: .2em;
  z-index: -1;
  display: block;
  text-decoration: none;
  color: hsl(0, 0%, 100%);
  font-size: 1em;
  width: 3em;
  height: 3em;
  border-radius: 50%;
  text-align: center;
  line-height: 3;
  background-color: hsla(0,0%,0%,.1);
  transition: transform .3s ease, background .2s ease;
}

.circular-menu .menu-item:hover {
  background-color: hsla(0,0%,0%,.3);
}

.circular-menu.active .menu-item {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.circular-menu.active .menu-item:nth-child(1) {
  transform: translate3d(1em,-7em,0);
}

.circular-menu.active .menu-item:nth-child(2) {
  transform: translate3d(-3.5em,-6.3em,0);
}

.circular-menu.active .menu-item:nth-child(3) {
  transform: translate3d(-6.5em,-3.2em,0);
}

.circular-menu.active .menu-item:nth-child(4) {
  transform: translate3d(-7em,1em,0);
}

/**
 * The other theme for this menu
 */

.circular-menu.circular-menu-left {
  right: auto; 
  left: 1em;
}

.circular-menu.circular-menu-left .floating-btn {
  background-color: hsl(217, 89%, 61%);
}

.circular-menu.circular-menu-left:after {
  background-color: hsl(217, 89%, 61%);
}

.circular-menu.circular-menu-left.active .floating-btn i {
  transform: rotate(90deg);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(1) {
  transform: translate3d(-1em,-7em,0);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(2) {
  transform: translate3d(3.5em,-6.3em,0);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(3) {
  transform: translate3d(6.5em,-3.2em,0);
}

.circular-menu.circular-menu-left.active .menu-item:nth-child(4) {
  transform: translate3d(7em,1em,0);
}
   
 /*Simple frame */
    .custom-frame {
      border: 1px solid #000;
      width: 90%;
      overflow: auto;
      border-radius: 10px;
      background: White;
      position: fixed;
      right:5% ;
      left: 5%;
      top: 50px;
    }
    .custom-size{
        width: 90%;
        height: 2px;
    }
    .-close {
        position: fixed;
       right: 7%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #ff4b2b 0%, #ff416c 100%);
        border: none;
        border-radius: 50%;
        color: #fff;
        font-size: 28px;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(255, 65, 108, 0.15);
        cursor: pointer;
        transition: background 0.3s, transform 0.2s, box-shadow 0.2s;
        z-index: 10;
    }
    .-close:hover, .-close:focus {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        color: #fff;
        transform: scale(1.1) rotate(10deg);
        box-shadow: 0 6px 18px rgba(255, 65, 108, 0.25);
        outline: none;
    }

   /*Note pad */
  button {
            background-color: #007bff;
            color: #fff;
            border: none;
            padding: 5px 10px;
            margin: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        select, input[type="color"] {
            padding: 5px;
        }

        textarea {
            width: 96%;
            height: 400px;
            resize: none;
            border-radius: 7px;
        }

        #pageIndicator {
            font-weight: bold;
            margin: 10px 0;
        }

        .page-transition {
            transition: opacity 0.3s;
        }  
    
    
    
     /* Admin cantent  */
   

    .iframe-container {
      border: 1px solid #000;
      width: 90%;
      overflow: auto;
      border-radius: 10px;
      background: White;
      position: fixed;
      display:none;
      right:5% ;
      left: 5%;
      top: 50px;
      
    }

    .popup {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid #000;
      background: #fff;
      padding: 20px;
    }
    
    
/* Admin css */
  .custom-content {
    border: 1px solid #000;
    padding: 20px;
    margin: 20px;
  }

  .custom-iframe-container {
    border: 1px solid #000;
    width: 90%;
    overflow: auto;
    border-radius: 10px;
    background: White;
    position: fixed;
    right:5% ;
    left: 5%;
    top: 50px;
    display:none;
  }

  .custom-popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid #000;
    background: #fff;
    padding: 20px;
  }
  
 /* html to file */ 
  .text {
            border-radius: 10px;width: 90%;height: 400px;
        }

/* copy  code css*/ 
  .copycode {
            white-space: nowrap;
            display: block;
            background-color: #f4f4f4;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            background: #828282;
            overflow-x: auto;
            border-radius: 5px;
            height: auto;
            font-family: 'Courier New', monospace;
        }






        
        