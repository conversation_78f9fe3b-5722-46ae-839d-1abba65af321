<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML/CSS/JS Compiler with AI</title>
    <link rel="stylesheet" href="main1.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
   
 <!-- Editor Start-->   
<script src='https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.12/ace.js' ></script>
<div class="Input" style ="z-index:-99;">   
<div style ="z-index:-99;" id="editor" class='same' stype="display:block">
</div></div>  
   <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.12/ext-language_tools.min.js"> </script>
 <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>  <!--end  Editor--> 


 
<!-- Two Menu  buttones -->
<div id="circularMenu" class="circular-menu">
    <a class="floating-btn" onclick="document.getElementById('circularMenu').classList.toggle('active');">
      <i class="fa fa-plus"></i>
    </a>
    <menu class="items-wrapper">
  
      <a  onclick="openCustomFrame('customFrame5')" class="menu-item fa fa-facebook"></a>
      <a onclick="openCustomFrame('customFrame6')" class="menu-item fa fa-twitter"></a>
      <a onclick="openCustomFrame('customFrame7')" class="menu-item fa fa-google-plus"></a>
      <a onclick="openCustomFrame('customFrame8')" class="menu-item fa fa-linkedin"></a>
    </menu>
  </div>
  
  
  <div id="circularMenu1" class="circular-menu circular-menu-left active">
    <a class="floating-btn" onclick="document.getElementById('circularMenu1').classList.toggle('active');">
      <i class="fa fa-bars"></i>
    </a>
    <menu class="items-wrapper">
     
      <a href="demo.html" target="_blank" class="menu-item fa fa-info-circle" title="Demo & Instructions"></a>
      <a onclick="openCustomFrame('customFrame2')" class="menu-item fa fa-home"></a>
     <a onclick="openCustomFrame('customFrameAI')" class="menu-item fa fa-magic" title="AI Code Generator"></a>
      <a onclick="openCustomFrame('customFrameComponents')" class="menu-item fa fa-code" title="Component Library"></a>
      <!-- Two Menu  buttones End  -->
    </menu>
</div>


        <!-- frame 5 online campailar -->
<div id="customFrameContainer">
    <div id="customFrame1" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame1', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame1')">&times;</span>  <br><br>
          </div></div>

<div id="customFrameContainer">
    <div id="customFrame2" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame2', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame2')">&times;</span>  <br><br>
          </div></div>


<div id="customFrameContainer">
    <div id="customFrame3" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame3', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame3')">&times;</span>  <br><br>
          </div></div>


<div id="customFrameContainer">
    <div id="customFrame4" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame4', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame4')">&times;</span>  <br><br>
          </div></div>











<div id="customFrameContainer">
    <div id="customFrame5" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame5', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame5')">&times;</span>  <br><br>
          <iframe src="daya.html" width="100%" height="100%"></iframe>

          </div></div>


<div id="customFrameContainer">
    <div id="customFrame6" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame6', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame6')">&times;</span>  <br><br>
          <iframe src="htmltofile.html" width="100%" height="100%"></iframe>
          </div></div>


<div id="customFrameContainer">
    <div id="customFrame7" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame7', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame7')">&times;</span>  
          <iframe src="notepad.html" width="100%" height="100%"></iframe>
          </div></div>



<div id="customFrameContainer">
    <div id="customFrame8" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrame8', this.value)" class="custom-size">
          <span class="-close"  onclick="closeCustomFrame('customFrame8')">&times;</span>  <br><br>
          <iframe style=" width: 100%;height: 100%;" src="https://www.scholarhat.com/compiler/html"></iframe>
          </div></div>

<!-- AI Code Generator Frame -->
<div id="customFrameContainer">
    <div id="customFrameAI" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrameAI', this.value)" class="custom-size">
        <span class="-close" onclick="closeCustomFrame('customFrameAI')">&times;</span>
        <div class="ai-generator-container">
            <h2>🤖 AI Code Generator</h2>
            <div class="ai-input-section">
                <label for="aiPrompt">Describe what you want to create:</label>
                <textarea id="aiPrompt" placeholder="Example: Create a responsive navigation bar with dropdown menu" rows="3"></textarea>
                <div class="ai-options">
                    <label>
                        <input type="radio" name="codeType" value="html" checked> HTML
                    </label>
                    <label>
                        <input type="radio" name="codeType" value="css"> CSS
                    </label>
                    <label>
                        <input type="radio" name="codeType" value="javascript"> JavaScript
                    </label>
                    <label>
                        <input type="radio" name="codeType" value="complete"> Complete Page
                    </label>
                </div>
                <button id="generateBtn" onclick="generateAICode()">Generate Code</button>
                <div id="aiStatus" class="ai-status"></div>
            </div>
            <div class="ai-output-section">
                <div class="ai-output-header">
                    <h3>Generated Code:</h3>
                    <button onclick="insertAICode()" id="insertBtn" style="display:none;">Insert into Editor</button>
                </div>
                <pre id="aiOutput" class="ai-code-output"></pre>
            </div>
        </div>
    </div>
</div>

<!-- Component Library Frame -->
<div id="customFrameContainer">
    <div id="customFrameComponents" class="custom-frame" style="height: 600px ; display: none;">
        <input type="range" min="25" max="750" value="600" oninput="resizeCustomFrame('customFrameComponents', this.value)" class="custom-size">
        <span class="-close" onclick="closeCustomFrame('customFrameComponents')">&times;</span>
        <div class="component-library-container">
            <h2>📚 Component Library</h2>
            <div class="component-search-section">
                <input type="text" id="componentSearch" placeholder="Search components (e.g., button, form, navbar)" oninput="searchComponents()">
                <div class="component-filters">
                    <button class="filter-btn active" onclick="filterComponents('all')">All</button>
                    <button class="filter-btn" onclick="filterComponents('html')">HTML</button>
                    <button class="filter-btn" onclick="filterComponents('css')">CSS</button>
                    <button class="filter-btn" onclick="filterComponents('javascript')">JavaScript</button>
                </div>
            </div>
            <div id="componentResults" class="component-results">
                <!-- Components will be loaded here -->
            </div>
        </div>
    </div>
</div>


</body>

<script src="main1.js"></script>
</html>