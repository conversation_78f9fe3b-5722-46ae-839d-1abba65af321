# 🚀 AI-Enhanced HTML/CSS/JavaScript Compiler

A powerful, user-friendly web-based compiler for HTML, CSS, and JavaScript with integrated AI code generation and a comprehensive component library.

## ✨ Features

### 🤖 AI Code Generator
- **Smart Code Generation**: Describe what you want to create in natural language
- **Multiple Code Types**: Generate HTML, CSS, JavaScript, or complete pages
- **Intelligent Parsing**: AI understands context and generates appropriate code
- **One-Click Insertion**: Generated code can be inserted directly into the editor

### 📚 Component Library
- **Searchable Components**: Find components by name, description, or tags
- **Multiple Categories**: HTML, CSS, and JavaScript components
- **Live Preview**: Preview components before inserting them
- **Ready-to-Use**: Professional, tested components for rapid development

### 💻 Live Editor
- **Real-Time Preview**: See changes instantly with live preview
- **Syntax Highlighting**: Powered by Ace Editor
- **Resizable Panels**: Customize your workspace
- **Auto-Save**: Your work is automatically saved to session storage

### 🎯 Pre-Built Components Include:
- **Buttons**: Primary, gradient, animated buttons
- **Navigation**: Responsive navbars with mobile support
- **Forms**: Contact forms with validation
- **Cards**: Product cards, content cards
- **Layouts**: Flexbox and CSS Grid systems
- **Animations**: CSS animations and transitions
- **Interactive Elements**: Modals, sliders, and more

## 🎮 How to Use

### Getting Started
1. Open `index.html` in your browser
2. Use the circular menu buttons to access different tools
3. The left menu (blue) contains main tools including AI and Component Library
4. The right menu contains additional utilities

### AI Code Generator
1. Click the **magic wand icon** (🪄) in the left menu
2. Describe what you want to create in the text area
3. Select the code type (HTML, CSS, JavaScript, or Complete Page)
4. Click **Generate Code**
5. Review the generated code and click **Insert into Editor**

**Example Prompts:**
- "Create a responsive navigation bar with dropdown menu"
- "Make a gradient button with hover animation"
- "Build a contact form with validation"
- "Create a CSS grid layout for a photo gallery"

### Component Library
1. Click the **code icon** (</>) in the left menu
2. Browse components or use the search bar
3. Filter by type (HTML, CSS, JavaScript)
4. Click on any component to insert it, or use the Preview button
5. Components are automatically inserted into your editor

### Live Preview
- Press **Ctrl+I** to toggle the live preview panel
- Resize the preview panel by dragging its left edge
- Changes in the editor are reflected immediately in the preview

## 🛠️ Technical Details

### Built With
- **Ace Editor**: Professional code editor with syntax highlighting
- **Font Awesome**: Icons for the user interface
- **Interact.js**: Resizable and draggable panels
- **Vanilla JavaScript**: No heavy frameworks, fast and lightweight

### File Structure
```
├── index.html          # Main application
├── main1.css          # Styles for the application
├── main1.js           # JavaScript functionality
├── demo.html          # Demo and instructions page
├── daya.html          # Sample header component
├── htmltofile.html    # HTML to file converter
├── notepad.html       # Built-in notepad
├── upload.html        # File upload utility
└── README.md          # This file
```

### AI Code Generation
The AI code generator uses intelligent pattern matching to create relevant code based on your descriptions. It recognizes keywords and generates appropriate HTML, CSS, or JavaScript code with modern best practices.

### Component Library
The component library contains over 10 carefully crafted components covering common web development needs. Each component is:
- **Responsive**: Works on all screen sizes
- **Modern**: Uses current web standards
- **Customizable**: Easy to modify and extend
- **Well-Documented**: Clear code structure

## 🎨 Customization

### Adding New Components
To add new components to the library, edit the `componentLibrary` array in `main1.js`:

```javascript
{
    id: 'your-component-id',
    name: 'Component Name',
    type: 'html', // or 'css', 'javascript'
    description: 'Component description',
    code: `<!-- Your component code here -->`,
    tags: ['tag1', 'tag2', 'tag3']
}
```

### Styling
Modify `main1.css` to customize the appearance. The application uses CSS custom properties for easy theming.

## 🚀 Future Enhancements

- **Real OpenAI Integration**: Connect to actual OpenAI API for more advanced code generation
- **More Components**: Expand the component library with additional elements
- **Templates**: Pre-built page templates for common use cases
- **Export Options**: Export projects as ZIP files or deploy to hosting services
- **Collaboration**: Real-time collaborative editing
- **Plugin System**: Extensible architecture for custom plugins

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

---

**Happy Coding!** 🎉