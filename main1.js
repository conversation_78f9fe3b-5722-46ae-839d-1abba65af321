     var [iframe,main,dubbing ] = [document.createElement("iframe"),document.createElement("main"),document.createElement("div")];      iframe.setAttribute('style','display:block;');main.classList = 'output same'; dubbing.classList = "ss"; main.append(iframe,dubbing); ss('.Input').after(main);
 function flow_load (theme) { var editor = ace.edit("editor");editor.setTheme(theme); editor.session.setMode("ace/mode/html");}    
 var editor = ace.edit("editor");editor.setTheme("ace/theme/chrome");editor.session.setMode("ace/mode/html"); var koli = editor;    
     //koli.getTheme() setOptions  ace koli.setOptions({cursorStyle:"smooth",behavioursEnabled:true,highlightGutterLine:true,animatedScroll:true, fontSize: "16px",foldStyle:"markbegin",showPrintMargin:0,enableLiveAutocompletion:true,enableBasicAutocompletion:true,}); 
   //sort Keys 
    koli.commands.addCommand({ name: "input_full",bindKey: {win: 'Ctrl-i',  mac: 'Command-i'}, exec: function(editor) {
       var action = ss('.output'),ed = ss('#editor');if(action.style.display == 'none'){action.style.display  = 'block'; if(action.style.width.length > 0){ ed.style.right = action.style.width } else { ed.style.right  = "20%";}flow_load (koli.getTheme())  }else { action.style.display  = 'none';  ed.style.right  = "0"; flow_load (koli.getTheme())}},
      readOnly: true // false if this command should not apply in readOnly mode
     });  
      // end sort key      
    //output
  koli.setValue(`<!DOCTYPE html>\n<html>\n<head>\n<meta charset="utf-8">\n<meta id="viewport" name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=2, viewport-fit=cover" />\n<title>white space</title></head>
<body>\n\n\n\n</body>\n</html>\n<style>\n\n\n</style>\n<!-- <script>\n\n</script>-->`);   // start value
    
    var sesssin =  window.sessionStorage.getItem('backup');  // current  bar session
         if(sesssin == null){
          iframe.srcdoc = koli.getValue();
         }else {
           koli.setValue(sesssin);
           iframe.srcdoc = sesssin;
         }
       
        koli.clearSelection() ;
        koli.on('change',function(){ 
          window.sessionStorage.setItem('backup',koli.getValue());
           iframe.srcdoc = koli.getValue();
       });  
    interact('.output').resizable({
    // resize from all edges and corners
    
    edges: { left: true, right: false, bottom: false, top: false },
    onstart: function (event) {ss('.ss').style.display = 'block'},
     onend  : function (event) {ss('.ss').style.display = 'none'},
    listeners: { 
      move (event) { 
        var target = event.target
        var x = (parseFloat(target.getAttribute('data-x')) || 0)
        var y = (parseFloat(target.getAttribute('data-y')) || 0)
             
        // update the element's style 
          ss('#editor').style.right = event.rect.width+"px"; 
           
        target.style.width = event.rect.width+"px"; //event.rect.width + 'px' 
           flow_load (koli.getTheme());
    
             
        
        x += event.deltaRect.left; 
       // y += event.deltaRect.top

        target.style.transform = 'translate(' + x + 'px,' + y + 'px)'

        target.setAttribute('data-x', x) 
        target.setAttribute('data-y', y)
       
      }
    },
    modifiers: [
      // keep the edges inside the parent
      interact.modifiers.restrictEdges({
        outer: 'parent'
      }),

      // minimum size 
      interact.modifiers.restrictSize({   
       min:{width:30},
       max:{width:1300}
      })
    ],
  
    inertia: true
  });
     
  



function ss(a){return document.querySelector(a)}
function all(x){return document.querySelectorAll(x)}    

// Simple frame
 function openCustomFrame(frameId) {
    document.getElementById(frameId).style.display = 'block';

    // Initialize component library when opened
    if (frameId === 'customFrameComponents') {
        initializeComponentLibrary();
    }
  }

  function closeCustomFrame(frameId) {
    document.getElementById(frameId).style.display = 'none';
  }

  function resizeCustomFrame(frameId, height) {
    document.getElementById(frameId).style.height = height + 'px';
    
    
  } 

// Not pad 
        let currentPage = 1;
        const notepad = document.getElementById("notepad");
        notepad.value = getNoteForPage(currentPage);

        function saveNote() {
            const note = notepad.value;
            localStorage.setItem(`notePage${currentPage}`, note);
        }

        function deleteNote() {
            notepad.value = "";
            localStorage.removeItem(`notePage${currentPage}`);
        }

        function downloadNote() {
            const note = notepad.value;
            const blob = new Blob([note], { type: "text/plain" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `notePage${currentPage}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function setBold() {
            notepad.style.fontWeight = "bold";
        }

        function changeFontSize(fontSize) {
            notepad.style.fontSize = fontSize + "px";
        }

        function changeTextColor(color) {
            notepad.style.color = color;
        }

        function previousPage() {
            if (currentPage > 1) {
                saveNote();
                currentPage--;
                updatePageIndicator();
                notepad.classList.add("page-transition");
                notepad.value = getNoteForPage(currentPage);
                setTimeout(() => notepad.classList.remove("page-transition"), 10);
            }
        }

        function nextPage() {
            saveNote();
            currentPage++;
            updatePageIndicator();
            notepad.classList.add("page-transition");
            notepad.value = getNoteForPage(currentPage);
            setTimeout(() => notepad.classList.remove("page-transition"), 10);
        }

        function updatePageIndicator() {
            document.getElementById("pageIndicator").textContent = `Page ${currentPage}`;
        }

        function getNoteForPage(page) {
            return localStorage.getItem(`notePage${page}`) || "";
        }

 

 // Html to file 
        document.getElementById("downloadButton").addEventListener("click", function () {
            const htmlCode = document.getElementById("htmlCode").value;
            const fileName = document.getElementById("fileName").value + ".html";
            const blob = new Blob([htmlCode], { type: "text/html" });
            const url = URL.createObjectURL(blob);

            const a = document.createElement("a");
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });


// code block
function copyCode() {
        const codeBlock = document.getElementById('codeBlock');
        const textArea = document.createElement('textarea');
        textArea.value = codeBlock.textContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Code copied to clipboard!');
    }

// AI Code Generator Functions
let generatedCode = '';

async function generateAICode() {
    const prompt = document.getElementById('aiPrompt').value.trim();
    const codeType = document.querySelector('input[name="codeType"]:checked').value;
    const generateBtn = document.getElementById('generateBtn');
    const status = document.getElementById('aiStatus');
    const output = document.getElementById('aiOutput');
    const insertBtn = document.getElementById('insertBtn');

    if (!prompt) {
        status.className = 'ai-status error';
        status.textContent = 'Please enter a description of what you want to create.';
        return;
    }

    // Show loading state
    generateBtn.disabled = true;
    generateBtn.textContent = 'Generating...';
    status.className = 'ai-status loading';
    status.textContent = 'Generating code with AI...';
    insertBtn.style.display = 'none';

    try {
        // Since we can't use OpenAI API directly from frontend without exposing API key,
        // we'll use a mock implementation that generates realistic code based on the prompt
        const code = await generateMockAICode(prompt, codeType);

        generatedCode = code;
        output.textContent = code;

        status.className = 'ai-status success';
        status.textContent = 'Code generated successfully!';
        insertBtn.style.display = 'inline-block';

    } catch (error) {
        status.className = 'ai-status error';
        status.textContent = 'Error generating code: ' + error.message;
    } finally {
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate Code';
    }
}

async function generateMockAICode(prompt, codeType) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const lowerPrompt = prompt.toLowerCase();

    // Generate code based on keywords in the prompt
    if (codeType === 'html' || codeType === 'complete') {
        if (lowerPrompt.includes('button')) {
            return generateButtonCode(lowerPrompt);
        } else if (lowerPrompt.includes('form')) {
            return generateFormCode(lowerPrompt);
        } else if (lowerPrompt.includes('nav') || lowerPrompt.includes('menu')) {
            return generateNavCode(lowerPrompt);
        } else if (lowerPrompt.includes('card')) {
            return generateCardCode(lowerPrompt);
        } else if (lowerPrompt.includes('table')) {
            return generateTableCode(lowerPrompt);
        } else {
            return generateGenericHTML(prompt);
        }
    } else if (codeType === 'css') {
        return generateCSSCode(prompt, lowerPrompt);
    } else if (codeType === 'javascript') {
        return generateJavaScriptCode(prompt, lowerPrompt);
    }

    return generateGenericHTML(prompt);
}

function generateButtonCode(prompt) {
    if (prompt.includes('gradient')) {
        return `<button class="gradient-btn">Click Me</button>

<style>
.gradient-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: transform 0.2s;
}

.gradient-btn:hover {
    transform: translateY(-2px);
}
</style>`;
    } else if (prompt.includes('animated')) {
        return `<button class="animated-btn">
    <span>Hover Me</span>
</button>

<style>
.animated-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
}

.animated-btn:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.animated-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.animated-btn:hover:before {
    left: 100%;
}
</style>`;
    } else {
        return `<button class="custom-btn">Click Me</button>

<style>
.custom-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.custom-btn:hover {
    background: #0056b3;
}
</style>`;
    }
}

function generateFormCode(prompt) {
    return `<form class="custom-form">
    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required>
    </div>
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required>
    </div>
    <div class="form-group">
        <label for="message">Message:</label>
        <textarea id="message" name="message" rows="4"></textarea>
    </div>
    <button type="submit">Submit</button>
</form>

<style>
.custom-form {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.custom-form button {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}

.custom-form button:hover {
    background: #218838;
}
</style>`;
}

function generateNavCode(prompt) {
    return `<nav class="navbar">
    <div class="nav-brand">
        <a href="#">Brand</a>
    </div>
    <ul class="nav-menu">
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#services">Services</a></li>
        <li><a href="#contact">Contact</a></li>
    </ul>
    <div class="hamburger">
        <span></span>
        <span></span>
        <span></span>
    </div>
</nav>

<style>
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #333;
    color: white;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #007bff;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }
}
</style>`;
}

function generateCardCode(prompt) {
    return `<div class="card">
    <img src="https://via.placeholder.com/300x200" alt="Card Image" class="card-image">
    <div class="card-content">
        <h3 class="card-title">Card Title</h3>
        <p class="card-description">This is a sample card description. You can add any content here.</p>
        <button class="card-button">Learn More</button>
    </div>
</div>

<style>
.card {
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 20px;
}

.card-title {
    margin: 0 0 10px 0;
    color: #333;
}

.card-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
}

.card-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.card-button:hover {
    background: #0056b3;
}
</style>`;
}

function generateTableCode(prompt) {
    return `<table class="custom-table">
    <thead>
        <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Role</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>John Doe</td>
            <td><EMAIL></td>
            <td>Developer</td>
            <td><button>Edit</button></td>
        </tr>
        <tr>
            <td>Jane Smith</td>
            <td><EMAIL></td>
            <td>Designer</td>
            <td><button>Edit</button></td>
        </tr>
    </tbody>
</table>

<style>
.custom-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.custom-table th,
.custom-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.custom-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.custom-table tr:hover {
    background: #f5f5f5;
}

.custom-table button {
    background: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}
</style>`;
}

function generateCSSCode(prompt, lowerPrompt) {
    if (lowerPrompt.includes('animation')) {
        return `/* CSS Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated-element {
    animation: fadeInUp 0.6s ease-out;
}

/* Pulse animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}`;
    } else if (lowerPrompt.includes('grid')) {
        return `/* CSS Grid Layout */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.grid-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Responsive grid */
@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
}`;
    } else {
        return `/* Custom CSS Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.btn:hover {
    background: #0056b3;
}

.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}`;
    }
}

function generateJavaScriptCode(prompt, lowerPrompt) {
    if (lowerPrompt.includes('click') || lowerPrompt.includes('button')) {
        return `// Button click handler
document.addEventListener('DOMContentLoaded', function() {
    const button = document.querySelector('.my-button');

    button.addEventListener('click', function() {
        alert('Button clicked!');

        // Add some visual feedback
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
});`;
    } else if (lowerPrompt.includes('form')) {
        return `// Form validation and submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Simple validation
        if (!data.email || !data.name) {
            alert('Please fill in all required fields');
            return;
        }

        // Simulate form submission
        console.log('Form data:', data);
        alert('Form submitted successfully!');
    });
});`;
    } else {
        return `// JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded successfully');

    // Example function
    function showMessage(message) {
        const div = document.createElement('div');
        div.textContent = message;
        div.style.cssText = 'padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;';
        document.body.appendChild(div);

        // Remove after 3 seconds
        setTimeout(() => {
            div.remove();
        }, 3000);
    }

    // Usage example
    showMessage('JavaScript is working!');
});`;
    }
}

function generateGenericHTML(prompt) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generated Content</h1>
        <p>This content was generated based on your request: "${prompt}"</p>
        <p>You can modify this HTML to better suit your needs.</p>
    </div>
</body>
</html>`;
}

function insertAICode() {
    if (generatedCode && koli) {
        // Insert the generated code into the editor
        const currentCode = koli.getValue();
        const newCode = currentCode + '\n\n' + generatedCode;
        koli.setValue(newCode);
        koli.clearSelection();

        // Show success message
        const status = document.getElementById('aiStatus');
        status.className = 'ai-status success';
        status.textContent = 'Code inserted into editor successfully!';

        // Close the AI frame after a short delay
        setTimeout(() => {
            closeCustomFrame('customFrameAI');
        }, 1500);
    }
}

// Component Library Functions
const componentLibrary = [
    {
        id: 'btn-primary',
        name: 'Primary Button',
        type: 'html',
        description: 'A styled primary button with hover effects',
        code: `<button class="btn-primary">Click Me</button>

<style>
.btn-primary {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #0056b3;
}
</style>`,
        tags: ['button', 'primary', 'blue']
    },
    {
        id: 'btn-gradient',
        name: 'Gradient Button',
        type: 'html',
        description: 'Beautiful gradient button with animation',
        code: `<button class="btn-gradient">Gradient Button</button>

<style>
.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: transform 0.2s;
}

.btn-gradient:hover {
    transform: translateY(-2px);
}
</style>`,
        tags: ['button', 'gradient', 'animated']
    },
    {
        id: 'card-basic',
        name: 'Basic Card',
        type: 'html',
        description: 'Simple card component with image and content',
        code: `<div class="card-basic">
    <img src="https://via.placeholder.com/300x200" alt="Card Image" class="card-image">
    <div class="card-content">
        <h3>Card Title</h3>
        <p>Card description goes here.</p>
        <button class="card-btn">Read More</button>
    </div>
</div>

<style>
.card-basic {
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 20px;
}

.card-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}
</style>`,
        tags: ['card', 'image', 'content']
    },
    {
        id: 'navbar-responsive',
        name: 'Responsive Navbar',
        type: 'html',
        description: 'Mobile-friendly navigation bar',
        code: `<nav class="navbar-responsive">
    <div class="nav-brand">Brand</div>
    <ul class="nav-menu">
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#contact">Contact</a></li>
    </ul>
    <div class="hamburger">
        <span></span>
        <span></span>
        <span></span>
    </div>
</nav>

<style>
.navbar-responsive {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #333;
    color: white;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
}

@media (max-width: 768px) {
    .nav-menu { display: none; }
    .hamburger { display: flex; }
}
</style>`,
        tags: ['navbar', 'navigation', 'responsive', 'mobile']
    },
    {
        id: 'form-contact',
        name: 'Contact Form',
        type: 'html',
        description: 'Complete contact form with validation',
        code: `<form class="contact-form">
    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required>
    </div>
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required>
    </div>
    <div class="form-group">
        <label for="message">Message:</label>
        <textarea id="message" name="message" rows="4" required></textarea>
    </div>
    <button type="submit">Send Message</button>
</form>

<style>
.contact-form {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.contact-form button {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}
</style>`,
        tags: ['form', 'contact', 'input', 'validation']
    },
    {
        id: 'css-flexbox',
        name: 'Flexbox Layout',
        type: 'css',
        description: 'Flexible box layout system',
        code: `/* Flexbox Container */
.flex-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.flex-item {
    flex: 1;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
    text-align: center;
}

/* Responsive flexbox */
@media (max-width: 768px) {
    .flex-container {
        flex-direction: column;
    }
}`,
        tags: ['flexbox', 'layout', 'responsive']
    },
    {
        id: 'css-grid',
        name: 'CSS Grid Layout',
        type: 'css',
        description: 'Modern grid layout system',
        code: `/* Grid Container */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.grid-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Grid areas */
.grid-header {
    grid-column: 1 / -1;
}`,
        tags: ['grid', 'layout', 'css']
    },
    {
        id: 'js-modal',
        name: 'Modal Dialog',
        type: 'javascript',
        description: 'Interactive modal dialog with JavaScript',
        code: `// Modal functionality
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// HTML structure needed:
/*
<div id="myModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal('myModal')">&times;</span>
        <h2>Modal Title</h2>
        <p>Modal content goes here.</p>
    </div>
</div>
*/`,
        tags: ['modal', 'dialog', 'popup', 'javascript']
    },
    {
        id: 'js-slider',
        name: 'Image Slider',
        type: 'javascript',
        description: 'Simple image slider with navigation',
        code: `// Image Slider
class ImageSlider {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.slides = this.container.querySelectorAll('.slide');
        this.currentSlide = 0;
        this.init();
    }

    init() {
        this.showSlide(0);
        this.createNavigation();
    }

    showSlide(index) {
        this.slides.forEach((slide, i) => {
            slide.style.display = i === index ? 'block' : 'none';
        });
        this.currentSlide = index;
    }

    nextSlide() {
        const next = (this.currentSlide + 1) % this.slides.length;
        this.showSlide(next);
    }

    prevSlide() {
        const prev = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.showSlide(prev);
    }

    createNavigation() {
        const nav = document.createElement('div');
        nav.className = 'slider-nav';
        nav.innerHTML = \`
            <button onclick="slider.prevSlide()">❮</button>
            <button onclick="slider.nextSlide()">❯</button>
        \`;
        this.container.appendChild(nav);
    }
}

// Usage: const slider = new ImageSlider('slider-container');`,
        tags: ['slider', 'carousel', 'images', 'navigation']
    }
];

let filteredComponents = [...componentLibrary];

function searchComponents() {
    const searchTerm = document.getElementById('componentSearch').value.toLowerCase();
    const activeFilter = document.querySelector('.filter-btn.active').textContent.toLowerCase();

    filteredComponents = componentLibrary.filter(component => {
        const matchesSearch = component.name.toLowerCase().includes(searchTerm) ||
                            component.description.toLowerCase().includes(searchTerm) ||
                            component.tags.some(tag => tag.includes(searchTerm));

        const matchesFilter = activeFilter === 'all' || component.type === activeFilter;

        return matchesSearch && matchesFilter;
    });

    displayComponents();
}

function filterComponents(type) {
    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    searchComponents(); // This will apply both search and filter
}

function displayComponents() {
    const resultsContainer = document.getElementById('componentResults');

    if (filteredComponents.length === 0) {
        resultsContainer.innerHTML = '<p style="text-align: center; color: #666;">No components found matching your search.</p>';
        return;
    }

    resultsContainer.innerHTML = filteredComponents.map(component => `
        <div class="component-item" onclick="selectComponent('${component.id}')">
            <h4>${component.name}</h4>
            <span class="component-type ${component.type}">${component.type.toUpperCase()}</span>
            <div class="component-description">${component.description}</div>
            <div class="component-preview">${component.code.substring(0, 100)}...</div>
            <div class="component-actions">
                <button class="insert-btn" onclick="event.stopPropagation(); insertComponent('${component.id}')">Insert</button>
                <button class="preview-btn" onclick="event.stopPropagation(); previewComponent('${component.id}')">Preview</button>
            </div>
        </div>
    `).join('');
}

function selectComponent(componentId) {
    insertComponent(componentId);
}

function insertComponent(componentId) {
    const component = componentLibrary.find(c => c.id === componentId);
    if (component && koli) {
        const currentCode = koli.getValue();
        const newCode = currentCode + '\n\n' + component.code;
        koli.setValue(newCode);
        koli.clearSelection();

        // Show success message
        alert(`${component.name} inserted into editor!`);

        // Close the component frame
        setTimeout(() => {
            closeCustomFrame('customFrameComponents');
        }, 1000);
    }
}

function previewComponent(componentId) {
    const component = componentLibrary.find(c => c.id === componentId);
    if (component) {
        // Create a preview window
        const previewWindow = window.open('', '_blank', 'width=600,height=400');
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Preview: ${component.name}</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .preview-header { background: #f8f9fa; padding: 10px; margin-bottom: 20px; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    <h2>Preview: ${component.name}</h2>
                    <p>${component.description}</p>
                </div>
                ${component.code}
            </body>
            </html>
        `);
        previewWindow.document.close();
    }
}

// Initialize component library when the frame is opened
function initializeComponentLibrary() {
    displayComponents();
}
}
    