<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap 5.3 Complete Reference</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --bs-primary-rgb: 13, 110, 253;
            --sidebar-width: 280px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
        }
        
        .bootstrap-reference {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        
        .sidebar {
            width: var(--sidebar-width);
            background: #fff;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        
        .sidebar-header h2 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .sidebar-header .version {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .search-container {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .search-input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            font-size: 0.875rem;
        }
        
        .nav-section {
            border-bottom: 1px solid #f1f3f4;
        }
        
        .nav-title {
            padding: 0.75rem 1rem;
            font-weight: 600;
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #f8f9fa;
            margin: 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .nav-item {
            display: block;
            padding: 0.5rem 1rem 0.5rem 2rem;
            color: #495057;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }
        
        .nav-item:hover {
            background: #f8f9fa;
            color: #0d6efd;
        }
        
        .nav-item.active {
            background: #e7f1ff;
            color: #0d6efd;
            border-right: 3px solid #0d6efd;
            font-weight: 500;
        }
        
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 2rem;
            overflow-y: auto;
            height: 100vh;
        }
        
        .content-header {
            margin-bottom: 2rem;
        }
        
        .breadcrumb-custom {
            font-size: 0.875rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .breadcrumb-custom a {
            color: #0d6efd;
            text-decoration: none;
        }
        
        .content-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            color: #212529;
        }
        
        .content-subtitle {
            font-size: 1.125rem;
            color: #6c757d;
            margin-bottom: 1.5rem;
        }
        
        .example-container {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            margin: 1.5rem 0;
            overflow: hidden;
        }
        
        .example-header {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
            font-size: 0.875rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .example-content {
            padding: 1.5rem;
        }
        
        .code-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            margin: 1rem 0;
            overflow: hidden;
        }
        
        .code-header {
            background: #e9ecef;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .code-content {
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow-x: auto;
            background: #fff;
        }
        
        .code-content pre {
            margin: 0;
            white-space: pre-wrap;
        }
        
        .btn-copy {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 0.875rem;
            cursor: pointer;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            transition: all 0.2s;
        }
        
        .btn-copy:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .quick-actions {
            display: flex;
            gap: 0.75rem;
            margin: 1.5rem 0;
            flex-wrap: wrap;
        }
        
        .cdn-info {
            background: #e7f3ff;
            border: 1px solid #b6d7ff;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .cdn-info h5 {
            color: #0d6efd;
            margin-bottom: 0.5rem;
        }
        
        .cdn-link {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            margin: 0.25rem 0;
            word-break: break-all;
        }
        
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .component-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            transition: all 0.2s;
        }
        
        .component-card:hover {
            border-color: #0d6efd;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .component-card h5 {
            color: #212529;
            margin-bottom: 0.75rem;
        }
        
        .component-card p {
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .hidden {
            display: none !important;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
            
            .content-title {
                font-size: 2rem;
            }
        }
        
        /* Syntax highlighting */
        .hljs-tag { color: #e83e8c; }
        .hljs-attr { color: #6f42c1; }
        .hljs-string { color: #198754; }
        .hljs-comment { color: #6c757d; font-style: italic; }
    </style>
</head>
<body>
    <div class="bootstrap-reference">
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fab fa-bootstrap"></i> Bootstrap</h2>
                <div class="version">v5.3.7 Complete Reference</div>
            </div>
            
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search Bootstrap docs..." id="searchInput" oninput="searchContent()">
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Getting Started</h6>
                <button class="nav-item active" onclick="showContent('introduction')">Introduction</button>
                <button class="nav-item" onclick="showContent('download')">Download</button>
                <button class="nav-item" onclick="showContent('contents')">Contents</button>
                <button class="nav-item" onclick="showContent('browsers-devices')">Browsers & devices</button>
                <button class="nav-item" onclick="showContent('javascript')">JavaScript</button>
                <button class="nav-item" onclick="showContent('webpack')">Webpack</button>
                <button class="nav-item" onclick="showContent('accessibility')">Accessibility</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Customize</h6>
                <button class="nav-item" onclick="showContent('customize-overview')">Overview</button>
                <button class="nav-item" onclick="showContent('sass')">Sass</button>
                <button class="nav-item" onclick="showContent('options')">Options</button>
                <button class="nav-item" onclick="showContent('color')">Color</button>
                <button class="nav-item" onclick="showContent('color-modes')">Color modes</button>
                <button class="nav-item" onclick="showContent('css-variables')">CSS variables</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Layout</h6>
                <button class="nav-item" onclick="showContent('breakpoints')">Breakpoints</button>
                <button class="nav-item" onclick="showContent('containers')">Containers</button>
                <button class="nav-item" onclick="showContent('grid')">Grid</button>
                <button class="nav-item" onclick="showContent('columns')">Columns</button>
                <button class="nav-item" onclick="showContent('gutters')">Gutters</button>
                <button class="nav-item" onclick="showContent('utilities')">Utilities</button>
                <button class="nav-item" onclick="showContent('z-index')">Z-index</button>
                <button class="nav-item" onclick="showContent('css-grid')">CSS Grid</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Content</h6>
                <button class="nav-item" onclick="showContent('reboot')">Reboot</button>
                <button class="nav-item" onclick="showContent('typography')">Typography</button>
                <button class="nav-item" onclick="showContent('images')">Images</button>
                <button class="nav-item" onclick="showContent('tables')">Tables</button>
                <button class="nav-item" onclick="showContent('figures')">Figures</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Forms</h6>
                <button class="nav-item" onclick="showContent('forms-overview')">Overview</button>
                <button class="nav-item" onclick="showContent('form-control')">Form control</button>
                <button class="nav-item" onclick="showContent('select')">Select</button>
                <button class="nav-item" onclick="showContent('checks-radios')">Checks & radios</button>
                <button class="nav-item" onclick="showContent('range')">Range</button>
                <button class="nav-item" onclick="showContent('input-group')">Input group</button>
                <button class="nav-item" onclick="showContent('floating-labels')">Floating labels</button>
                <button class="nav-item" onclick="showContent('layout')">Layout</button>
                <button class="nav-item" onclick="showContent('validation')">Validation</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Components</h6>
                <button class="nav-item" onclick="showContent('accordion')">Accordion</button>
                <button class="nav-item" onclick="showContent('alerts')">Alerts</button>
                <button class="nav-item" onclick="showContent('badge')">Badge</button>
                <button class="nav-item" onclick="showContent('breadcrumb')">Breadcrumb</button>
                <button class="nav-item" onclick="showContent('buttons')">Buttons</button>
                <button class="nav-item" onclick="showContent('button-group')">Button group</button>
                <button class="nav-item" onclick="showContent('card')">Card</button>
                <button class="nav-item" onclick="showContent('carousel')">Carousel</button>
                <button class="nav-item" onclick="showContent('close-button')">Close button</button>
                <button class="nav-item" onclick="showContent('collapse')">Collapse</button>
                <button class="nav-item" onclick="showContent('dropdowns')">Dropdowns</button>
                <button class="nav-item" onclick="showContent('list-group')">List group</button>
                <button class="nav-item" onclick="showContent('modal')">Modal</button>
                <button class="nav-item" onclick="showContent('navbar')">Navbar</button>
                <button class="nav-item" onclick="showContent('navs-tabs')">Navs & tabs</button>
                <button class="nav-item" onclick="showContent('offcanvas')">Offcanvas</button>
                <button class="nav-item" onclick="showContent('pagination')">Pagination</button>
                <button class="nav-item" onclick="showContent('placeholders')">Placeholders</button>
                <button class="nav-item" onclick="showContent('popovers')">Popovers</button>
                <button class="nav-item" onclick="showContent('progress')">Progress</button>
                <button class="nav-item" onclick="showContent('scrollspy')">Scrollspy</button>
                <button class="nav-item" onclick="showContent('spinners')">Spinners</button>
                <button class="nav-item" onclick="showContent('toasts')">Toasts</button>
                <button class="nav-item" onclick="showContent('tooltips')">Tooltips</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Helpers</h6>
                <button class="nav-item" onclick="showContent('clearfix')">Clearfix</button>
                <button class="nav-item" onclick="showContent('color-background')">Color & background</button>
                <button class="nav-item" onclick="showContent('colored-links')">Colored links</button>
                <button class="nav-item" onclick="showContent('focus-ring')">Focus ring</button>
                <button class="nav-item" onclick="showContent('position')">Position</button>
                <button class="nav-item" onclick="showContent('stacks')">Stacks</button>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-title">Utilities</h6>
                <button class="nav-item" onclick="showContent('api')">API</button>
                <button class="nav-item" onclick="showContent('background')">Background</button>
                <button class="nav-item" onclick="showContent('borders')">Borders</button>
                <button class="nav-item" onclick="showContent('colors')">Colors</button>
                <button class="nav-item" onclick="showContent('display')">Display</button>
                <button class="nav-item" onclick="showContent('flex')">Flex</button>
                <button class="nav-item" onclick="showContent('spacing')">Spacing</button>
                <button class="nav-item" onclick="showContent('text')">Text</button>
            </div>
        </nav>
        
        <main class="main-content" id="mainContent">
            <!-- Content will be loaded here -->
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>

    <!-- Bootstrap Content Data -->
    <script src="bootstrap-content.js"></script>

    <script>
        // Initialize with introduction content
        document.addEventListener('DOMContentLoaded', function() {
            showContent('introduction');
        });
    </script>
</body>
</html>
