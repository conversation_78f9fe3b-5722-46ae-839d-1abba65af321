<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Reference</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .reference-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: #fff;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            z-index: 100;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .sidebar-header h2 {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .search-box {
            width: 100%;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            background: rgba(255,255,255,0.2);
            color: white;
            placeholder-color: rgba(255,255,255,0.7);
        }
        
        .search-box::placeholder {
            color: rgba(255,255,255,0.7);
        }
        
        .nav-section {
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .nav-title {
            padding: 8px 20px;
            font-weight: 600;
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .nav-item {
            display: block;
            padding: 8px 20px 8px 35px;
            color: #495057;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .nav-item:hover {
            background: #f8f9fa;
            color: #007bff;
        }
        
        .nav-item.active {
            background: #e3f2fd;
            color: #1976d2;
            border-right: 3px solid #1976d2;
        }
        
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 30px;
            overflow-y: auto;
        }
        
        .content-header {
            margin-bottom: 30px;
        }
        
        .content-title {
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 10px;
            color: #212529;
        }
        
        .content-subtitle {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .breadcrumb-item {
            color: #007bff;
            text-decoration: none;
        }
        
        .breadcrumb-separator {
            color: #dee2e6;
        }
        
        .section {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .section h3 {
            font-size: 24px;
            margin-bottom: 15px;
            color: #212529;
        }
        
        .section h4 {
            font-size: 18px;
            margin: 20px 0 10px 0;
            color: #495057;
        }
        
        .section p {
            line-height: 1.6;
            margin-bottom: 15px;
            color: #6c757d;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .code-block pre {
            margin: 0;
            white-space: pre-wrap;
        }
        
        .example-box {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .example-header {
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 500;
            font-size: 14px;
        }
        
        .example-content {
            padding: 20px;
        }
        
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
        }
        
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-outline {
            background: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }
        
        .btn-outline:hover {
            background: #007bff;
            color: white;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            background: white;
        }
        
        .card h5 {
            margin-bottom: 10px;
            color: #212529;
        }
        
        .card p {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 0;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 20px;
            }
            
            .grid-2,
            .grid-3 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="reference-container">
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>📚 Web Reference</h2>
                <input type="text" class="search-box" placeholder="Search documentation..." id="searchBox" oninput="searchContent()">
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Getting Started</div>
                <a class="nav-item active" onclick="showContent('introduction')">Introduction</a>
                <a class="nav-item" onclick="showContent('download')">Download & Setup</a>
                <a class="nav-item" onclick="showContent('browsers')">Browser Support</a>
                <a class="nav-item" onclick="showContent('accessibility')">Accessibility</a>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Layout</div>
                <a class="nav-item" onclick="showContent('breakpoints')">Breakpoints</a>
                <a class="nav-item" onclick="showContent('containers')">Containers</a>
                <a class="nav-item" onclick="showContent('grid')">Grid System</a>
                <a class="nav-item" onclick="showContent('flexbox')">Flexbox</a>
                <a class="nav-item" onclick="showContent('positioning')">Positioning</a>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Content</div>
                <a class="nav-item" onclick="showContent('typography')">Typography</a>
                <a class="nav-item" onclick="showContent('images')">Images</a>
                <a class="nav-item" onclick="showContent('tables')">Tables</a>
                <a class="nav-item" onclick="showContent('figures')">Figures</a>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Forms</div>
                <a class="nav-item" onclick="showContent('form-controls')">Form Controls</a>
                <a class="nav-item" onclick="showContent('form-select')">Select</a>
                <a class="nav-item" onclick="showContent('form-checks')">Checks & Radios</a>
                <a class="nav-item" onclick="showContent('form-range')">Range</a>
                <a class="nav-item" onclick="showContent('input-group')">Input Groups</a>
                <a class="nav-item" onclick="showContent('form-validation')">Validation</a>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Components</div>
                <a class="nav-item" onclick="showContent('buttons')">Buttons</a>
                <a class="nav-item" onclick="showContent('cards')">Cards</a>
                <a class="nav-item" onclick="showContent('navbar')">Navigation</a>
                <a class="nav-item" onclick="showContent('modal')">Modals</a>
                <a class="nav-item" onclick="showContent('dropdown')">Dropdowns</a>
                <a class="nav-item" onclick="showContent('carousel')">Carousel</a>
                <a class="nav-item" onclick="showContent('accordion')">Accordion</a>
                <a class="nav-item" onclick="showContent('alerts')">Alerts</a>
                <a class="nav-item" onclick="showContent('badges')">Badges</a>
                <a class="nav-item" onclick="showContent('breadcrumb')">Breadcrumb</a>
                <a class="nav-item" onclick="showContent('pagination')">Pagination</a>
                <a class="nav-item" onclick="showContent('progress')">Progress</a>
                <a class="nav-item" onclick="showContent('spinners')">Spinners</a>
                <a class="nav-item" onclick="showContent('tooltips')">Tooltips</a>
                <a class="nav-item" onclick="showContent('popovers')">Popovers</a>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Utilities</div>
                <a class="nav-item" onclick="showContent('colors')">Colors</a>
                <a class="nav-item" onclick="showContent('spacing')">Spacing</a>
                <a class="nav-item" onclick="showContent('display')">Display</a>
                <a class="nav-item" onclick="showContent('borders')">Borders</a>
                <a class="nav-item" onclick="showContent('shadows')">Shadows</a>
                <a class="nav-item" onclick="showContent('sizing')">Sizing</a>
                <a class="nav-item" onclick="showContent('text-utilities')">Text</a>
                <a class="nav-item" onclick="showContent('background')">Background</a>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">JavaScript</div>
                <a class="nav-item" onclick="showContent('js-events')">Events</a>
                <a class="nav-item" onclick="showContent('js-dom')">DOM Manipulation</a>
                <a class="nav-item" onclick="showContent('js-ajax')">AJAX</a>
                <a class="nav-item" onclick="showContent('js-animations')">Animations</a>
                <a class="nav-item" onclick="showContent('js-validation')">Form Validation</a>
            </div>
        </nav>
        
        <main class="main-content" id="mainContent">
            <!-- Content will be loaded here -->
        </main>
    </div>

    <script>
        // Content data structure
        const contentData = {
            introduction: {
                title: "Introduction",
                subtitle: "Get started with modern web development",
                breadcrumb: ["Getting Started", "Introduction"],
                content: `
                    <div class="section">
                        <h3>Welcome to Web Development Reference</h3>
                        <p>This comprehensive reference guide provides everything you need to build modern, responsive websites using HTML, CSS, and JavaScript. Whether you're a beginner or an experienced developer, you'll find practical examples and best practices here.</p>
                        
                        <h4>What's Included</h4>
                        <div class="grid-3">
                            <div class="card">
                                <h5>🏗️ Layout Systems</h5>
                                <p>CSS Grid, Flexbox, responsive design patterns, and modern layout techniques.</p>
                            </div>
                            <div class="card">
                                <h5>🎨 Components</h5>
                                <p>Ready-to-use UI components like buttons, cards, navigation, modals, and more.</p>
                            </div>
                            <div class="card">
                                <h5>⚡ JavaScript</h5>
                                <p>Interactive functionality, DOM manipulation, events, and modern JavaScript features.</p>
                            </div>
                        </div>
                        
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getStarterTemplate())">📋 Insert Starter Template</button>
                            <button class="btn btn-outline" onclick="showContent('download')">📥 Setup Guide</button>
                        </div>
                    </div>
                `
            },
            
            buttons: {
                title: "Buttons",
                subtitle: "Button styles and interactive elements",
                breadcrumb: ["Components", "Buttons"],
                content: `
                    <div class="section">
                        <h3>Button Styles</h3>
                        <p>Create beautiful, accessible buttons with various styles and states.</p>
                        
                        <div class="example-box">
                            <div class="example-header">Basic Buttons</div>
                            <div class="example-content">
                                <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin: 4px;">Primary</button>
                                <button style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin: 4px;">Secondary</button>
                                <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin: 4px;">Success</button>
                                <button style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin: 4px;">Danger</button>
                            </div>
                        </div>
                        
                        <div class="code-block">
                            <pre>&lt;!-- Primary Button --&gt;
&lt;button class="btn btn-primary"&gt;Primary&lt;/button&gt;

&lt;!-- Secondary Button --&gt;
&lt;button class="btn btn-secondary"&gt;Secondary&lt;/button&gt;

&lt;style&gt;
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}
&lt;/style&gt;</pre>
                        </div>
                        
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getButtonCode())">📋 Insert Button Code</button>
                        </div>
                    </div>
                `
            },
            
            grid: {
                title: "Grid System",
                subtitle: "Responsive grid layouts with CSS Grid and Flexbox",
                breadcrumb: ["Layout", "Grid System"],
                content: `
                    <div class="section">
                        <h3>CSS Grid Layout</h3>
                        <p>Create powerful, flexible layouts with CSS Grid. Perfect for complex designs and responsive layouts.</p>
                        
                        <div class="example-box">
                            <div class="example-header">Basic Grid</div>
                            <div class="example-content">
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                                    <div style="background: #e3f2fd; padding: 20px; text-align: center; border-radius: 4px;">Item 1</div>
                                    <div style="background: #e8f5e8; padding: 20px; text-align: center; border-radius: 4px;">Item 2</div>
                                    <div style="background: #fff3e0; padding: 20px; text-align: center; border-radius: 4px;">Item 3</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="code-block">
                            <pre>&lt;div class="grid-container"&gt;
    &lt;div class="grid-item"&gt;Item 1&lt;/div&gt;
    &lt;div class="grid-item"&gt;Item 2&lt;/div&gt;
    &lt;div class="grid-item"&gt;Item 3&lt;/div&gt;
&lt;/div&gt;

&lt;style&gt;
.grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.grid-item {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 4px;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr;
    }
}
&lt;/style&gt;</pre>
                        </div>
                        
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getGridCode())">📋 Insert Grid Code</button>
                        </div>
                    </div>
                `
            },

            typography: {
                title: "Typography",
                subtitle: "Text styling and font management",
                breadcrumb: ["Content", "Typography"],
                content: `
                    <div class="section">
                        <h3>Typography System</h3>
                        <p>Create beautiful, readable text with consistent typography scales and responsive font sizes.</p>

                        <div class="example-box">
                            <div class="example-header">Headings</div>
                            <div class="example-content">
                                <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem;">Heading 1</h1>
                                <h2 style="font-size: 2rem; margin-bottom: 0.5rem;">Heading 2</h2>
                                <h3 style="font-size: 1.75rem; margin-bottom: 0.5rem;">Heading 3</h3>
                                <h4 style="font-size: 1.5rem; margin-bottom: 0.5rem;">Heading 4</h4>
                                <h5 style="font-size: 1.25rem; margin-bottom: 0.5rem;">Heading 5</h5>
                                <h6 style="font-size: 1rem; margin-bottom: 0.5rem;">Heading 6</h6>
                            </div>
                        </div>

                        <div class="code-block">
                            <pre>&lt;h1&gt;Main Heading&lt;/h1&gt;
&lt;h2&gt;Section Heading&lt;/h2&gt;
&lt;p class="lead"&gt;Lead paragraph with larger text.&lt;/p&gt;
&lt;p&gt;Regular paragraph text.&lt;/p&gt;

&lt;style&gt;
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }

.lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
}

p {
    margin-bottom: 1rem;
    line-height: 1.6;
}
&lt;/style&gt;</pre>
                        </div>

                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getTypographyCode())">📋 Insert Typography</button>
                        </div>
                    </div>
                `
            },

            cards: {
                title: "Cards",
                subtitle: "Flexible content containers",
                breadcrumb: ["Components", "Cards"],
                content: `
                    <div class="section">
                        <h3>Card Components</h3>
                        <p>Cards provide a flexible container for displaying content in a structured way.</p>

                        <div class="example-box">
                            <div class="example-header">Basic Card</div>
                            <div class="example-content">
                                <div style="max-width: 300px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <img src="https://via.placeholder.com/300x200" style="width: 100%; height: 200px; object-fit: cover;">
                                    <div style="padding: 20px;">
                                        <h5 style="margin-bottom: 10px;">Card Title</h5>
                                        <p style="color: #6c757d; margin-bottom: 15px;">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
                                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px;">Go somewhere</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="code-block">
                            <pre>&lt;div class="card"&gt;
    &lt;img src="image.jpg" class="card-img-top" alt="..."&gt;
    &lt;div class="card-body"&gt;
        &lt;h5 class="card-title"&gt;Card title&lt;/h5&gt;
        &lt;p class="card-text"&gt;Card description text.&lt;/p&gt;
        &lt;a href="#" class="btn btn-primary"&gt;Go somewhere&lt;/a&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;style&gt;
.card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-img-top {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-body {
    padding: 20px;
}

.card-title {
    margin-bottom: 10px;
    font-size: 1.25rem;
}

.card-text {
    color: #6c757d;
    margin-bottom: 15px;
}
&lt;/style&gt;</pre>
                        </div>

                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getCardCode())">📋 Insert Card Code</button>
                        </div>
                    </div>
                `
            },

            navbar: {
                title: "Navigation",
                subtitle: "Responsive navigation components",
                breadcrumb: ["Components", "Navigation"],
                content: `
                    <div class="section">
                        <h3>Navigation Bar</h3>
                        <p>Create responsive navigation bars with dropdowns and mobile-friendly hamburger menus.</p>

                        <div class="example-box">
                            <div class="example-header">Responsive Navbar</div>
                            <div class="example-content">
                                <nav style="background: #343a40; padding: 1rem 2rem; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="color: white; font-size: 1.25rem; font-weight: bold;">Brand</div>
                                    <div style="display: flex; gap: 2rem;">
                                        <a href="#" style="color: white; text-decoration: none;">Home</a>
                                        <a href="#" style="color: white; text-decoration: none;">About</a>
                                        <a href="#" style="color: white; text-decoration: none;">Services</a>
                                        <a href="#" style="color: white; text-decoration: none;">Contact</a>
                                    </div>
                                </nav>
                            </div>
                        </div>

                        <div class="code-block">
                            <pre>&lt;nav class="navbar"&gt;
    &lt;div class="navbar-brand"&gt;Brand&lt;/div&gt;
    &lt;ul class="navbar-nav"&gt;
        &lt;li&gt;&lt;a href="#"&gt;Home&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#"&gt;About&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#"&gt;Services&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#"&gt;Contact&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;
    &lt;div class="navbar-toggle"&gt;☰&lt;/div&gt;
&lt;/nav&gt;

&lt;style&gt;
.navbar {
    background: #343a40;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    color: white;
    font-size: 1.25rem;
    font-weight: bold;
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.navbar-nav a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

.navbar-nav a:hover {
    color: #007bff;
}

.navbar-toggle {
    display: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

@media (max-width: 768px) {
    .navbar-nav {
        display: none;
    }

    .navbar-toggle {
        display: block;
    }
}
&lt;/style&gt;</pre>
                        </div>

                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getNavbarCode())">📋 Insert Navbar Code</button>
                        </div>
                    </div>
                `
            },

            modal: {
                title: "Modals",
                subtitle: "Dialog boxes and overlays",
                breadcrumb: ["Components", "Modals"],
                content: `
                    <div class="section">
                        <h3>Modal Dialogs</h3>
                        <p>Create accessible modal dialogs for displaying content in overlay windows.</p>

                        <div class="example-box">
                            <div class="example-header">Modal Example</div>
                            <div class="example-content">
                                <button onclick="openDemoModal()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px;">Open Modal</button>
                            </div>
                        </div>

                        <div class="code-block">
                            <pre>&lt;!-- Modal Trigger --&gt;
&lt;button onclick="openModal('myModal')"&gt;Open Modal&lt;/button&gt;

&lt;!-- Modal --&gt;
&lt;div id="myModal" class="modal"&gt;
    &lt;div class="modal-content"&gt;
        &lt;div class="modal-header"&gt;
            &lt;h4&gt;Modal Title&lt;/h4&gt;
            &lt;span class="close" onclick="closeModal('myModal')"&gt;&times;&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="modal-body"&gt;
            &lt;p&gt;Modal content goes here...&lt;/p&gt;
        &lt;/div&gt;
        &lt;div class="modal-footer"&gt;
            &lt;button onclick="closeModal('myModal')"&gt;Close&lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;style&gt;
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.modal-content {
    background: white;
    margin: 5% auto;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}
&lt;/style&gt;

&lt;script&gt;
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';
}
&lt;/script&gt;</pre>
                        </div>

                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="insertToParent(getModalCode())">📋 Insert Modal Code</button>
                        </div>
                    </div>
                `
            }
        };

        // Current active content
        let currentContent = 'introduction';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showContent('introduction');
        });

        function showContent(contentId) {
            const content = contentData[contentId];
            if (!content) return;
            
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
            
            // Build breadcrumb
            const breadcrumbHtml = content.breadcrumb.map((item, index) => {
                if (index === content.breadcrumb.length - 1) {
                    return item;
                } else {
                    return `<a href="#" class="breadcrumb-item">${item}</a> <span class="breadcrumb-separator">/</span>`;
                }
            }).join(' ');
            
            // Update main content
            document.getElementById('mainContent').innerHTML = `
                <div class="content-header">
                    <div class="breadcrumb">${breadcrumbHtml}</div>
                    <h1 class="content-title">${content.title}</h1>
                    <p class="content-subtitle">${content.subtitle}</p>
                </div>
                ${content.content}
            `;
            
            currentContent = contentId;
        }

        function searchContent() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const navItems = document.querySelectorAll('.nav-item');
            
            navItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = searchTerm ? 'none' : 'block';
                }
            });
        }

        function insertToParent(code) {
            if (window.parent && window.parent.koli) {
                const currentCode = window.parent.koli.getValue();
                const newCode = currentCode + '\n\n' + code;
                window.parent.koli.setValue(newCode);
                window.parent.koli.clearSelection();
                alert('Code inserted into editor!');
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(code).then(() => {
                    alert('Code copied to clipboard!');
                });
            }
        }

        function getStarterTemplate() {
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Web Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to My Website</h1>
        <p>Start building your amazing web page here!</p>
    </div>
</body>
</html>`;
        }

        function getButtonCode() {
            return `<button class="btn btn-primary">Primary Button</button>
<button class="btn btn-secondary">Secondary Button</button>

<style>
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    margin: 4px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}
</style>`;
        }

        function getGridCode() {
            return `<div class="grid-container">
    <div class="grid-item">Item 1</div>
    <div class="grid-item">Item 2</div>
    <div class="grid-item">Item 3</div>
    <div class="grid-item">Item 4</div>
    <div class="grid-item">Item 5</div>
    <div class="grid-item">Item 6</div>
</div>

<style>
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.grid-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr;
        padding: 10px;
    }
}
</style>`;
        }

        function getTypographyCode() {
            return `<h1>Main Heading</h1>
<h2>Section Heading</h2>
<h3>Subsection Heading</h3>

<p class="lead">This is a lead paragraph with larger text that stands out.</p>
<p>This is a regular paragraph with normal text size and spacing.</p>

<blockquote class="blockquote">
    <p>This is a blockquote for highlighting important quotes or text.</p>
    <footer>— Author Name</footer>
</blockquote>

<style>
/* Typography System */
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 0.5rem;
    color: #212529;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: #6c757d;
}

.lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
    color: #495057;
}

.blockquote {
    margin: 1rem 0;
    padding: 1rem;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.blockquote p {
    margin-bottom: 0.5rem;
    font-style: italic;
}

.blockquote footer {
    font-size: 0.875rem;
    color: #6c757d;
}
</style>`;
        }

        function getCardCode() {
            return `<div class="card">
    <img src="https://via.placeholder.com/300x200" class="card-img-top" alt="Card image">
    <div class="card-body">
        <h5 class="card-title">Card Title</h5>
        <p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
        <a href="#" class="btn btn-primary">Go somewhere</a>
    </div>
</div>

<style>
.card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    background: white;
    max-width: 350px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-img-top {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-body {
    padding: 20px;
}

.card-title {
    margin-bottom: 10px;
    font-size: 1.25rem;
    font-weight: 500;
    color: #212529;
}

.card-text {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}
</style>`;
        }

        function getNavbarCode() {
            return `<nav class="navbar">
    <div class="navbar-brand">Your Brand</div>
    <ul class="navbar-nav">
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#services">Services</a></li>
        <li><a href="#contact">Contact</a></li>
    </ul>
    <div class="navbar-toggle" onclick="toggleMobileMenu()">☰</div>
</nav>

<style>
.navbar {
    background: #343a40;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-brand {
    color: white;
    font-size: 1.25rem;
    font-weight: bold;
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.navbar-nav li {
    margin: 0;
}

.navbar-nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 0;
    transition: color 0.3s;
}

.navbar-nav a:hover {
    color: #007bff;
}

.navbar-toggle {
    display: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .navbar-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #343a40;
        flex-direction: column;
        padding: 1rem 2rem;
        gap: 1rem;
    }

    .navbar-nav.active {
        display: flex;
    }

    .navbar-toggle {
        display: block;
    }
}
</style>

<script>
function toggleMobileMenu() {
    const nav = document.querySelector('.navbar-nav');
    nav.classList.toggle('active');
}
</script>`;
        }

        function getModalCode() {
            return `<!-- Modal Trigger Button -->
<button onclick="openModal('myModal')" class="btn btn-primary">Open Modal</button>

<!-- Modal -->
<div id="myModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h4>Modal Title</h4>
            <span class="close" onclick="closeModal('myModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>This is the modal content. You can put any HTML content here.</p>
            <p>Forms, images, text, or any other elements can be placed inside the modal.</p>
        </div>
        <div class="modal-footer">
            <button onclick="closeModal('myModal')" class="btn btn-secondary">Close</button>
            <button class="btn btn-primary">Save Changes</button>
        </div>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background: white;
    margin: 5% auto;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    animation: slideIn 0.3s;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    color: #212529;
}

.modal-body {
    padding: 20px;
    color: #6c757d;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    text-align: right;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s;
}

.close:hover {
    color: #dc3545;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
</style>

<script>
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}
</script>`;
        }

        function openDemoModal() {
            alert('This would open a modal dialog. Click "Insert Modal Code" to get the full implementation!');
        }
    </script>
</body>
</html>
