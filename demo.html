<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Enhanced HTML Compiler Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .feature-card p {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .demo-section h2 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .demo-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .demo-item h4 {
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .cta {
            text-align: center;
            margin-top: 40px;
        }
        
        .cta-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #ffd700;
            margin-bottom: 20px;
        }
        
        .instructions ol {
            line-height: 1.8;
        }
        
        .instructions li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI-Enhanced HTML Compiler</h1>
            <p>Create stunning web pages with AI assistance and a comprehensive component library</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <span class="feature-icon">🤖</span>
                <h3>AI Code Generator</h3>
                <p>Describe what you want to create and let AI generate the HTML, CSS, or JavaScript code for you. Perfect for rapid prototyping and learning.</p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">📚</span>
                <h3>Component Library</h3>
                <p>Browse and search through a curated collection of HTML, CSS, and JavaScript components. Click to insert directly into your editor.</p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3>Live Preview</h3>
                <p>See your changes in real-time with the built-in live preview. Resize, edit, and test your code instantly.</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 What You Can Create</h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>Buttons</h4>
                    <p>Primary, gradient, animated buttons with hover effects</p>
                </div>
                <div class="demo-item">
                    <h4>Navigation</h4>
                    <p>Responsive navbars with mobile hamburger menus</p>
                </div>
                <div class="demo-item">
                    <h4>Forms</h4>
                    <p>Contact forms, login forms with validation</p>
                </div>
                <div class="demo-item">
                    <h4>Cards</h4>
                    <p>Product cards, profile cards, content cards</p>
                </div>
                <div class="demo-item">
                    <h4>Layouts</h4>
                    <p>Flexbox and CSS Grid responsive layouts</p>
                </div>
                <div class="demo-item">
                    <h4>Animations</h4>
                    <p>CSS animations, transitions, and effects</p>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 How to Use</h3>
            <ol>
                <li><strong>Open the Compiler:</strong> Click the blue menu button (left side) to access tools</li>
                <li><strong>AI Generator:</strong> Click the magic wand icon to open the AI code generator</li>
                <li><strong>Component Library:</strong> Click the code icon to browse pre-built components</li>
                <li><strong>Describe Your Needs:</strong> Type what you want to create (e.g., "responsive navigation bar")</li>
                <li><strong>Generate & Insert:</strong> Click generate, then insert the code into your editor</li>
                <li><strong>Live Preview:</strong> Use Ctrl+I to toggle the live preview panel</li>
                <li><strong>Customize:</strong> Edit the generated code to match your exact needs</li>
            </ol>
        </div>
        
        <div class="cta">
            <a href="index.html" class="cta-button">🚀 Start Creating Now</a>
        </div>
    </div>
</body>
</html>
