<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Library</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .component-library-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .component-library-container h2 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .component-search-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        #componentSearch {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin-bottom: 15px;
            box-sizing: border-box;
        }
        
        .component-filters {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .filter-btn:hover {
            background: #f8f9fa;
        }
        
        .filter-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .component-results {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .component-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .component-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .component-item h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 16px;
        }
        
        .component-type {
            display: inline-block;
            padding: 2px 8px;
            background: #e9ecef;
            border-radius: 12px;
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .component-type.html {
            background: #fff3cd;
            color: #856404;
        }
        
        .component-type.css {
            background: #d4edda;
            color: #155724;
        }
        
        .component-type.javascript {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .component-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .component-preview {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #333;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 10px;
        }
        
        .component-actions {
            display: flex;
            gap: 8px;
        }
        
        .insert-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            flex: 1;
        }
        
        .insert-btn:hover {
            background: #218838;
        }
        
        .preview-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            flex: 1;
        }
        
        .preview-btn:hover {
            background: #138496;
        }
        
        .copy-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            flex: 1;
        }
        
        .copy-btn:hover {
            background: #5a6268;
        }
        
        .no-results {
            text-align: center;
            color: #666;
            padding: 40px;
            font-style: italic;
        }
        
        /* Scrollbar styling */
        .component-results::-webkit-scrollbar {
            width: 8px;
        }
        
        .component-results::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .component-results::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .component-results::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        @media (max-width: 768px) {
            .component-filters {
                justify-content: center;
            }
            
            .component-results {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="component-library-container">
        <h2>📚 Component Library</h2>
        
        <div class="component-search-section">
            <input type="text" id="componentSearch" placeholder="Search components (e.g., button, form, navbar)" oninput="searchComponents()">
            <div class="component-filters">
                <button class="filter-btn active" onclick="filterComponents('all', this)">All</button>
                <button class="filter-btn" onclick="filterComponents('html', this)">HTML</button>
                <button class="filter-btn" onclick="filterComponents('css', this)">CSS</button>
                <button class="filter-btn" onclick="filterComponents('javascript', this)">JavaScript</button>
            </div>
        </div>
        
        <div id="componentResults" class="component-results">
            <!-- Components will be loaded here -->
        </div>
    </div>

    <script>
        const componentLibrary = [
            {
                id: 'btn-primary',
                name: 'Primary Button',
                type: 'html',
                description: 'A styled primary button with hover effects',
                code: `<button class="btn-primary">Click Me</button>

<style>
.btn-primary {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #0056b3;
}
</style>`,
                tags: ['button', 'primary', 'blue']
            },
            {
                id: 'btn-gradient',
                name: 'Gradient Button',
                type: 'html',
                description: 'Beautiful gradient button with animation',
                code: `<button class="btn-gradient">Gradient Button</button>

<style>
.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: transform 0.2s;
}

.btn-gradient:hover {
    transform: translateY(-2px);
}
</style>`,
                tags: ['button', 'gradient', 'animated']
            },
            {
                id: 'card-basic',
                name: 'Basic Card',
                type: 'html',
                description: 'Simple card component with image and content',
                code: `<div class="card-basic">
    <img src="https://via.placeholder.com/300x200" alt="Card Image" class="card-image">
    <div class="card-content">
        <h3>Card Title</h3>
        <p>Card description goes here.</p>
        <button class="card-btn">Read More</button>
    </div>
</div>

<style>
.card-basic {
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 20px;
}

.card-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}
</style>`,
                tags: ['card', 'image', 'content']
            },
            {
                id: 'navbar-responsive',
                name: 'Responsive Navbar',
                type: 'html',
                description: 'Mobile-friendly navigation bar',
                code: `<nav class="navbar-responsive">
    <div class="nav-brand">Brand</div>
    <ul class="nav-menu">
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#contact">Contact</a></li>
    </ul>
    <div class="hamburger">
        <span></span>
        <span></span>
        <span></span>
    </div>
</nav>

<style>
.navbar-responsive {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #333;
    color: white;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
}

@media (max-width: 768px) {
    .nav-menu { display: none; }
    .hamburger { display: flex; }
}
</style>`,
                tags: ['navbar', 'navigation', 'responsive', 'mobile']
            },
            {
                id: 'form-contact',
                name: 'Contact Form',
                type: 'html',
                description: 'Complete contact form with validation',
                code: `<form class="contact-form">
    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required>
    </div>
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required>
    </div>
    <div class="form-group">
        <label for="message">Message:</label>
        <textarea id="message" name="message" rows="4" required></textarea>
    </div>
    <button type="submit">Send Message</button>
</form>

<style>
.contact-form {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.contact-form button {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}
</style>`,
                tags: ['form', 'contact', 'input', 'validation']
            }
        ];

        let filteredComponents = [...componentLibrary];
        let currentFilter = 'all';

        function searchComponents() {
            const searchTerm = document.getElementById('componentSearch').value.toLowerCase();
            
            filteredComponents = componentLibrary.filter(component => {
                const matchesSearch = component.name.toLowerCase().includes(searchTerm) ||
                                    component.description.toLowerCase().includes(searchTerm) ||
                                    component.tags.some(tag => tag.includes(searchTerm));
                
                const matchesFilter = currentFilter === 'all' || component.type === currentFilter;
                
                return matchesSearch && matchesFilter;
            });
            
            displayComponents();
        }

        function filterComponents(type, buttonElement) {
            currentFilter = type;
            
            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            buttonElement.classList.add('active');
            
            searchComponents(); // This will apply both search and filter
        }

        function displayComponents() {
            const resultsContainer = document.getElementById('componentResults');
            
            if (filteredComponents.length === 0) {
                resultsContainer.innerHTML = '<div class="no-results">No components found matching your search.</div>';
                return;
            }
            
            resultsContainer.innerHTML = filteredComponents.map(component => `
                <div class="component-item">
                    <h4>${component.name}</h4>
                    <span class="component-type ${component.type}">${component.type.toUpperCase()}</span>
                    <div class="component-description">${component.description}</div>
                    <div class="component-preview">${component.code.substring(0, 100)}...</div>
                    <div class="component-actions">
                        <button class="insert-btn" onclick="insertComponent('${component.id}')">Insert</button>
                        <button class="preview-btn" onclick="previewComponent('${component.id}')">Preview</button>
                        <button class="copy-btn" onclick="copyComponent('${component.id}')">Copy</button>
                    </div>
                </div>
            `).join('');
        }

        function insertComponent(componentId) {
            const component = componentLibrary.find(c => c.id === componentId);
            if (component) {
                if (window.parent && window.parent.koli) {
                    const currentCode = window.parent.koli.getValue();
                    const newCode = currentCode + '\n\n' + component.code;
                    window.parent.koli.setValue(newCode);
                    window.parent.koli.clearSelection();
                    alert(`${component.name} inserted into editor!`);
                } else {
                    // Fallback: copy to clipboard
                    copyComponent(componentId);
                }
            }
        }

        function previewComponent(componentId) {
            const component = componentLibrary.find(c => c.id === componentId);
            if (component) {
                const previewWindow = window.open('', '_blank', 'width=600,height=400');
                previewWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Preview: ${component.name}</title>
                        <style>
                            body { font-family: Arial, sans-serif; padding: 20px; }
                            .preview-header { background: #f8f9fa; padding: 10px; margin-bottom: 20px; border-radius: 5px; }
                        </style>
                    </head>
                    <body>
                        <div class="preview-header">
                            <h2>Preview: ${component.name}</h2>
                            <p>${component.description}</p>
                        </div>
                        ${component.code}
                    </body>
                    </html>
                `);
                previewWindow.document.close();
            }
        }

        function copyComponent(componentId) {
            const component = componentLibrary.find(c => c.id === componentId);
            if (component) {
                navigator.clipboard.writeText(component.code).then(() => {
                    alert(`${component.name} code copied to clipboard!`);
                });
            }
        }

        // Initialize component library when page loads
        window.onload = function() {
            displayComponents();
        };
    </script>
</body>
</html>
