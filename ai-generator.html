<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .ai-generator-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .ai-generator-container h2 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .ai-input-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .ai-input-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        #aiPrompt {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
            min-height: 80px;
        }
        
        .ai-options {
            margin: 15px 0;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .ai-options label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: normal;
            cursor: pointer;
        }
        
        #generateBtn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        #generateBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        #generateBtn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .ai-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .ai-status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .ai-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .ai-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .ai-output-section {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .ai-output-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .ai-output-header h3 {
            margin: 0;
            color: #333;
        }
        
        #insertBtn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: none;
        }
        
        #insertBtn:hover {
            background: #218838;
        }
        
        .ai-code-output {
            background: #f8f9fa;
            padding: 15px;
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .example-prompts {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .example-prompts h4 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .example-prompts ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .example-prompts li {
            margin-bottom: 5px;
            cursor: pointer;
            color: #0066cc;
        }
        
        .example-prompts li:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="ai-generator-container">
        <h2>🤖 AI Code Generator</h2>
        
        <div class="example-prompts">
            <h4>💡 Example Prompts (Click to use):</h4>
            <ul>
                <li onclick="setPrompt('Create a gradient button with hover animation')">Create a gradient button with hover animation</li>
                <li onclick="setPrompt('Build a responsive navigation bar')">Build a responsive navigation bar</li>
                <li onclick="setPrompt('Make a contact form with validation')">Make a contact form with validation</li>
                <li onclick="setPrompt('Create a product card with image')">Create a product card with image</li>
                <li onclick="setPrompt('Design a CSS grid layout')">Design a CSS grid layout</li>
            </ul>
        </div>
        
        <div class="ai-input-section">
            <label for="aiPrompt">Describe what you want to create:</label>
            <textarea id="aiPrompt" placeholder="Example: Create a responsive navigation bar with dropdown menu"></textarea>
            <div class="ai-options">
                <label>
                    <input type="radio" name="codeType" value="html" checked> HTML
                </label>
                <label>
                    <input type="radio" name="codeType" value="css"> CSS
                </label>
                <label>
                    <input type="radio" name="codeType" value="javascript"> JavaScript
                </label>
                <label>
                    <input type="radio" name="codeType" value="complete"> Complete Page
                </label>
            </div>
            <button type="button" id="generateBtn" onclick="generateAICode()">Generate Code</button>
            <div id="aiStatus" class="ai-status"></div>
        </div>
        
        <div class="ai-output-section">
            <div class="ai-output-header">
                <h3>Generated Code:</h3>
                <button type="button" onclick="copyToClipboard()" id="copyBtn" style="display:none;">Copy Code</button>
                <button type="button" onclick="insertToParent()" id="insertBtn">Insert into Editor</button>
            </div>
            <pre id="aiOutput" class="ai-code-output">Generated code will appear here...</pre>
        </div>
    </div>

    <script>
        let generatedCode = '';

        function setPrompt(prompt) {
            document.getElementById('aiPrompt').value = prompt;
        }

        async function generateAICode() {
            const prompt = document.getElementById('aiPrompt').value.trim();
            const codeType = document.querySelector('input[name="codeType"]:checked').value;
            const generateBtn = document.getElementById('generateBtn');
            const status = document.getElementById('aiStatus');
            const output = document.getElementById('aiOutput');
            const copyBtn = document.getElementById('copyBtn');

            if (!prompt) {
                status.className = 'ai-status error';
                status.textContent = 'Please enter a description of what you want to create.';
                return;
            }

            // Show loading state
            generateBtn.disabled = true;
            generateBtn.textContent = 'Generating...';
            status.className = 'ai-status loading';
            status.textContent = 'Generating code with AI...';
            copyBtn.style.display = 'none';

            try {
                const code = await generateMockAICode(prompt, codeType);
                
                generatedCode = code;
                output.textContent = code;
                
                status.className = 'ai-status success';
                status.textContent = 'Code generated successfully!';
                copyBtn.style.display = 'inline-block';
                
            } catch (error) {
                status.className = 'ai-status error';
                status.textContent = 'Error generating code: ' + error.message;
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = 'Generate Code';
            }
        }

        async function generateMockAICode(prompt, codeType) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            const lowerPrompt = prompt.toLowerCase();
            
            // Generate code based on keywords in the prompt
            if (codeType === 'html' || codeType === 'complete') {
                if (lowerPrompt.includes('button')) {
                    return generateButtonCode(lowerPrompt);
                } else if (lowerPrompt.includes('form')) {
                    return generateFormCode(lowerPrompt);
                } else if (lowerPrompt.includes('nav') || lowerPrompt.includes('menu')) {
                    return generateNavCode(lowerPrompt);
                } else if (lowerPrompt.includes('card')) {
                    return generateCardCode(lowerPrompt);
                } else if (lowerPrompt.includes('table')) {
                    return generateTableCode(lowerPrompt);
                } else {
                    return generateGenericHTML(prompt);
                }
            } else if (codeType === 'css') {
                return generateCSSCode(prompt, lowerPrompt);
            } else if (codeType === 'javascript') {
                return generateJavaScriptCode(prompt, lowerPrompt);
            }
            
            return generateGenericHTML(prompt);
        }

        function generateButtonCode(prompt) {
            if (prompt.includes('gradient')) {
                return `<button class="gradient-btn">Click Me</button>

<style>
.gradient-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: transform 0.2s;
}

.gradient-btn:hover {
    transform: translateY(-2px);
}
</style>`;
            } else if (prompt.includes('animated')) {
                return `<button class="animated-btn">
    <span>Hover Me</span>
</button>

<style>
.animated-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
}

.animated-btn:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.animated-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.animated-btn:hover:before {
    left: 100%;
}
</style>`;
            } else {
                return `<button class="custom-btn">Click Me</button>

<style>
.custom-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.custom-btn:hover {
    background: #0056b3;
}
</style>`;
            }
        }

        function generateFormCode(prompt) {
            return `<form class="custom-form">
    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required>
    </div>
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required>
    </div>
    <div class="form-group">
        <label for="message">Message:</label>
        <textarea id="message" name="message" rows="4"></textarea>
    </div>
    <button type="submit">Submit</button>
</form>

<style>
.custom-form {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.custom-form button {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}

.custom-form button:hover {
    background: #218838;
}
</style>`;
        }

        function generateNavCode(prompt) {
            return `<nav class="navbar">
    <div class="nav-brand">
        <a href="#">Brand</a>
    </div>
    <ul class="nav-menu">
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#services">Services</a></li>
        <li><a href="#contact">Contact</a></li>
    </ul>
    <div class="hamburger">
        <span></span>
        <span></span>
        <span></span>
    </div>
</nav>

<style>
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #333;
    color: white;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #007bff;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }
}
</style>`;
        }

        function generateCardCode(prompt) {
            return `<div class="card">
    <img src="https://via.placeholder.com/300x200" alt="Card Image" class="card-image">
    <div class="card-content">
        <h3 class="card-title">Card Title</h3>
        <p class="card-description">This is a sample card description. You can add any content here.</p>
        <button class="card-button">Learn More</button>
    </div>
</div>

<style>
.card {
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 20px;
}

.card-title {
    margin: 0 0 10px 0;
    color: #333;
}

.card-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
}

.card-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.card-button:hover {
    background: #0056b3;
}
</style>`;
        }

        function generateTableCode(prompt) {
            return `<table class="custom-table">
    <thead>
        <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Role</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>John Doe</td>
            <td><EMAIL></td>
            <td>Developer</td>
            <td><button>Edit</button></td>
        </tr>
        <tr>
            <td>Jane Smith</td>
            <td><EMAIL></td>
            <td>Designer</td>
            <td><button>Edit</button></td>
        </tr>
    </tbody>
</table>

<style>
.custom-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.custom-table th,
.custom-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.custom-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.custom-table tr:hover {
    background: #f5f5f5;
}

.custom-table button {
    background: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}
</style>`;
        }

        function generateCSSCode(prompt, lowerPrompt) {
            if (lowerPrompt.includes('animation')) {
                return `/* CSS Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated-element {
    animation: fadeInUp 0.6s ease-out;
}

/* Pulse animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}`;
            } else if (lowerPrompt.includes('grid')) {
                return `/* CSS Grid Layout */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.grid-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Responsive grid */
@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
}`;
            } else {
                return `/* Custom CSS Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.btn:hover {
    background: #0056b3;
}

.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}`;
            }
        }

        function generateJavaScriptCode(prompt, lowerPrompt) {
            if (lowerPrompt.includes('click') || lowerPrompt.includes('button')) {
                return `// Button click handler
document.addEventListener('DOMContentLoaded', function() {
    const button = document.querySelector('.my-button');

    button.addEventListener('click', function() {
        alert('Button clicked!');

        // Add some visual feedback
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
});`;
            } else if (lowerPrompt.includes('form')) {
                return `// Form validation and submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Simple validation
        if (!data.email || !data.name) {
            alert('Please fill in all required fields');
            return;
        }

        // Simulate form submission
        console.log('Form data:', data);
        alert('Form submitted successfully!');
    });
});`;
            } else {
                return `// JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded successfully');

    // Example function
    function showMessage(message) {
        const div = document.createElement('div');
        div.textContent = message;
        div.style.cssText = 'padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;';
        document.body.appendChild(div);

        // Remove after 3 seconds
        setTimeout(() => {
            div.remove();
        }, 3000);
    }

    // Usage example
    showMessage('JavaScript is working!');
});`;
            }
        }

        function generateGenericHTML(prompt) {
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generated Content</h1>
        <p>This content was generated based on your request: "${prompt}"</p>
        <p>You can modify this HTML to better suit your needs.</p>
    </div>
</body>
</html>`;
        }

        function copyToClipboard() {
            if (generatedCode) {
                navigator.clipboard.writeText(generatedCode).then(() => {
                    const status = document.getElementById('aiStatus');
                    status.className = 'ai-status success';
                    status.textContent = 'Code copied to clipboard!';
                });
            }
        }

        function insertToParent() {
            if (generatedCode && window.parent && window.parent.koli) {
                const currentCode = window.parent.koli.getValue();
                const newCode = currentCode + '\n\n' + generatedCode;
                window.parent.koli.setValue(newCode);
                window.parent.koli.clearSelection();

                const status = document.getElementById('aiStatus');
                status.className = 'ai-status success';
                status.textContent = 'Code inserted into editor successfully!';
            } else if (generatedCode) {
                // Fallback: copy to clipboard
                copyToClipboard();
            }
        }
    </script>
</body>
</html>
